# Mergen AI Dashboard - Detailed Frontend Analysis & Implementation Guide

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [Design System Deep Dive](#design-system-deep-dive)
3. [Component Implementation Details](#component-implementation-details)
4. [State Management & Data Flow](#state-management--data-flow)
5. [API Integration Patterns](#api-integration-patterns)
6. [Styling & Animation System](#styling--animation-system)
7. [Performance & Optimization](#performance--optimization)
8. [Implementation Checklist](#implementation-checklist)

## Architecture Overview

### Technology Stack
```json
{
  "frontend": {
    "framework": "React 18.3.1",
    "language": "TypeScript",
    "bundler": "Vite",
    "styling": "Tailwind CSS 4.0.14",
    "animations": "Framer Motion 12.5.0",
    "ui_library": "Ant Design 5.24.4",
    "routing": "React Router DOM 7.3.0",
    "http_client": "Axios 1.8.4"
  },
  "backend": {
    "runtime": "Node.js",
    "framework": "Express",
    "database": "MongoDB",
    "auth": "JWT tokens",
    "file_upload": "Multer middleware"
  }
}
```

### Project Structure
```
frontend/src/
├── components/
│   ├── common/
│   │   └── SearchBar.tsx          # Reusable input component
│   ├── dashboard/
│   │   ├── Dashboard.tsx          # Main orchestrator
│   │   ├── HeaderSection.tsx      # Navigation & user menu
│   │   ├── FooterSection.tsx      # Chat interface container
│   │   ├── ListViewSection.tsx    # Results container
│   │   ├── ListCards.tsx          # Individual result cards
│   │   ├── HistoryDrawer.tsx      # Conversation sidebar
│   │   └── emailSection/
│   │       └── EmailSection.tsx   # Email modal
│   └── report/
│       └── ReportSection.tsx      # Report generation
├── services/                      # API layer
├── hooks/                         # Custom React hooks
├── context/                       # Global state providers
└── assets/                        # Images, fonts, icons
```

## Design System Deep Dive

### Color System
```css
/* Primary Colors */
:root {
  --color-primary-dark: #1E1E1E;     /* Main UI elements */
  --color-secondary-dark: #353535;    /* Tags, buttons */
  --color-tertiary-dark: #414141;     /* Chat background */
  --color-text-primary: #222222;      /* Main text */
  
  /* Background Colors */
  --color-bg-primary: #FFFFFF;        /* Main background */
  --color-bg-secondary: #F6F6F6;      /* Input fields */
  --color-bg-tertiary: #ECECEC;       /* Secondary surfaces */
  
  /* Border Colors */
  --color-border-primary: #939393;    /* Main borders */
  --color-border-secondary: #9E9E9E;  /* Card borders */
  
  /* Dynamic Card Colors */
  --color-card-yellow: #F8DE7E;       /* Light yellow cards */
  --color-card-peach: #E8AE77;        /* Peach cards */
  --color-card-gray: #D3D3D3;         /* Gray cards */
}
```

### Typography Scale
```css
/* Font Families */
@font-face {
  font-family: 'Inter';
  src: url('./assets/fonts/Inter/Inter-VariableFont_opsz,wght.ttf');
  font-weight: 100 900;
}

@font-face {
  font-family: 'Charter';
  src: url('./assets/fonts/charter/ttf/Charter Regular.ttf');
  font-weight: 100 900;
}

/* Typography Scale */
.text-micro { font-size: 5px; }      /* Tiny labels */
.text-xs { font-size: 6px; }         /* Keywords */
.text-sm { font-size: 8px; }         /* Body text */
.text-base { font-size: 12px; }      /* Standard UI */
.text-lg { font-size: 15px; }        /* Larger UI text */
.text-xl { font-size: 20px; }        /* Headings */
.text-2xl { font-size: 30px; }       /* Large headings */
.text-display { font-size: 70px; }   /* Date displays */
```

### Layout Grid System
```css
/* Responsive Grid Classes */
.grid-responsive {
  @apply columns-1 gap-0;
  @apply md:columns-2;
  @apply lg:columns-3;
  @apply xl:columns-4;
}

/* Container Heights */
.container-card { height: calc(100vh - 400px); }
.container-list { height: calc(100vh - 146px); }
.container-history { height: calc(100vh - 270px); }

/* Responsive Widths */
.width-drawer { width: 279px; }
.width-list-desktop { width: calc(100% - 279px); }
.width-list-mobile { width: 100%; }
```

## Component Implementation Details

### SearchBar Component
```typescript
interface SearchBarProps {
  color: string;
  border: boolean;
  inputValue: string;
  handleSearch: () => void;
  setInputValue: (value: string) => void;
  placeholder: string;
  resize: boolean;
  disable: boolean;
  onPaste?: React.ClipboardEventHandler<HTMLTextAreaElement | HTMLInputElement>;
}

const SearchBar: React.FC<SearchBarProps> = ({
  color, border, handleSearch, setInputValue, inputValue,
  placeholder, disable, resize, onPaste
}) => {
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  const MAX_HEIGHT = 144; // 6 lines max

  const adjustHeight = useCallback(() => {
    if (textAreaRef.current && resize) {
      textAreaRef.current.style.height = 'auto';
      const scrollHeight = textAreaRef.current.scrollHeight;
      textAreaRef.current.style.height = 
        `${Math.min(scrollHeight, MAX_HEIGHT)}px`;
    }
  }, [resize]);

  return (
    <div className="relative">
      {resize ? (
        <textarea
          ref={textAreaRef}
          rows={1}
          style={{
            resize: "none",
            lineHeight: "24px",
            height: resize ? "auto" : "56px",
            overflowY: resize ? "auto" : "hidden",
          }}
          className={`w-[95%] bg-[#F6F6F6] py-4 rounded-[25px] ps-7 pe-14 outline-none textarea-hide-scrollbar ${
            border ? "border border-black" : ""
          }`}
          onChange={(e) => {
            setInputValue(e.target.value);
            adjustHeight();
          }}
          onKeyDown={(e) => {
            if (e.key === "Enter" && !disable) {
              handleSearch();
            }
          }}
          onPaste={onPaste}
          placeholder={placeholder}
          value={inputValue}
        />
      ) : (
        <input
          type="text"
          className={`w-[95%] bg-[#F6F6F6] py-4 rounded-[25px] ps-7 pe-16 outline-none ${
            border ? "border border-black" : ""
          }`}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter" && !disable) {
              handleSearch();
            }
          }}
          onPaste={onPaste}
          placeholder={placeholder}
          value={inputValue}
        />
      )}
      
      <button
        className={`bg-[${color || "#1E1E1E"}] py-4.5 px-6 rounded-[25px] absolute ${
          border ? "-top-[1px]" : "top-0"
        } -right-2 ${!disable ? "cursor-pointer" : "cursor-not-allowed"}`}
        onClick={handleSearch}
        disabled={disable}
      >
        <img src={arrowIcon} alt="icon" height={17} width={20} />
      </button>
    </div>
  );
};
```

### Dashboard State Management
```typescript
interface DashboardState {
  // Input & UI State
  inputValue: string;
  openEmailSection: boolean;
  openHistoryView: boolean;
  isSticky: boolean;
  
  // Conversation Management
  allConversations: Conversation[];
  activeConversation: string;
  activeChat: string;
  
  // Message Management
  messages: Message[];
  currMessage: Message[];
  reply: string;
  activePairIndex: number;
  
  // Results Data
  referenceData: Reference[];
}

const DashboardSection: React.FC<{ isListView: boolean }> = ({ isListView }) => {
  const { setLoading } = useLoader();
  const { screenWidth } = useWindowResize();
  
  // State declarations
  const [inputValue, setInputValue] = useState<string>("");
  const [allConversations, setAllConversations] = useState<Conversation[]>([]);
  const [activeConversation, setActiveConversation] = useState<string>("");
  const [activeChat, setActiveChat] = useState<string>("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [currMessage, setCurrMessage] = useState<Message[]>([]);
  const [referenceData, setReferenceData] = useState<Reference[]>([]);
  const [activePairIndex, setActivePairIndex] = useState<number>(0);
  
  // Message handling
  const handleSearch = async () => {
    try {
      setLoading(true);
      
      const messageData = await createMessage({
        chatId: activeChat,
        content: inputValue,
        sender: "user",
        attachedFiles: {},
      });
      
      await createReference({
        messageId: messageData?.data?.aiMessageCreated?._id,
        content: messageData?.data?.references,
      });
      
      // Update UI state
      setInputValue("");
      await fetchDataView("", "");
      
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };
  
  // Data fetching
  const fetchDataView = async (userActiveId: string, aiActiveId: string) => {
    if (activeConversation) {
      try {
        setLoading(true);
        
        const chatData = await getChatByConversationId(activeConversation);
        const messageData = await getMessageByChatId(chatData.data[0]._id);
        
        const filteredMessages = userActiveId && aiActiveId
          ? messageData.data.filter((item: Message) => 
              item._id === userActiveId || item._id === aiActiveId
            )
          : [];
          
        const payload = userActiveId && aiActiveId
          ? filteredMessages.find(msg => msg._id === aiActiveId)?._id
          : messageData.data[1]._id;
          
        const referenceData = await getReferencesByMessageId(payload);
        
        setActiveChat(chatData.data[0]._id);
        setCurrMessage(filteredMessages.length ? filteredMessages : messageData.data);
        setMessages(messageData.data);
        setReferenceData(referenceData.map((item: any) => item.content).flat(1));
        
      } catch (error) {
        console.error("Error:", error);
      } finally {
        setLoading(false);
      }
    }
  };
  
  return (
    <motion.div className="flex">
      <AnimatePresence>
        {openHistoryView && (
          <motion.div
            initial={{ x: "-100%", width: 0, opacity: 0 }}
            animate={{ x: 0, width: "279px", opacity: 1 }}
            exit={{ x: "-100%", width: 0, opacity: 0 }}
            transition={{ duration: 0.6, ease: "easeInOut" }}
            className="absolute lg:static w-[279px] bg-drawer z-50"
          >
            <HistoryDrawer
              setOpenHistoryView={setOpenHistoryView}
              allConversations={allConversations}
              activeConversation={activeConversation}
              setActiveConversation={setActiveConversation}
              getAllConversations={getAllConversations}
              setOpenEmailSection={setOpenEmailSection}
              activeChat={activeChat}
            />
          </motion.div>
        )}
      </AnimatePresence>
      
      <motion.div
        animate={{
          width: openHistoryView
            ? screenWidth > 1023 ? "calc(100% - 279px)" : "100%"
            : "100%",
        }}
        transition={{ duration: 0.6, ease: "easeInOut" }}
        className="relative overflow-auto scrollbar-custom listCardContainer"
      >
        {isListView ? (
          <ListViewSection referenceData={referenceData} />
        ) : (
          <div className="px-6 sm:px-16 mt-6 scrollbar-custom overflow-auto cardContainer">
            <CardSectionArea />
          </div>
        )}
        
        <div className="footer-Section">
          <FooterSection
            messages={currMessage}
            reply={reply}
            handleSearch={handleSearch}
            setInputValue={setInputValue}
            inputValue={inputValue}
            openEmailSection={openEmailSection}
            setOpenEmailSection={setOpenEmailSection}
            activeChat={activeChat}
            handleNext={handleNext}
            handlePrevious={handlePrevious}
            activePairIndex={activePairIndex}
            maxPair={Math.floor(messages.length / 2)}
          />
        </div>
      </motion.div>
    </motion.div>
  );
};
```

### ListCards Component Structure
```typescript
interface ListCardData {
  summary: string;
  keywords: string[];
  key_points: string[];
}

const ListCards: React.FC<{ data: ListCardData }> = ({ data }) => {
  const { summary, keywords: Keywords, key_points: Important_items } = data;
  const [selectedItem, setSelectedItem] = useState<ListCardData | null>(null);
  const [summaryData, setSummarydata] = useState("");
  const [impItems, setImpItems] = useState<string[]>([]);
  const [keywords, setKeywords] = useState<string[]>([]);

  return (
    <div className="flex border-t-4 border-[#939393]">
      {/* Desktop Summary Section */}
      <div className="hidden lg:block w-[60%] border-x-[0.5px] border-[#9E9E9E]">
        <div className="text-black px-5 py-6 flex flex-col gap-4 ps-12">
          <h1 className="font-semibold text-[9px] leading-[100%] tracking-[-0.04em]">
            Summary
          </h1>
          <p className="font-medium text-[8px] leading-[198%] tracking-[-0.04em] truncate-text">
            {summary}
          </p>
          <div className="flex justify-end">
            <span
              className="w-fit px-5 bg-[#353535] text-white py-2.5 rounded-[22px] font-black text-[6px] leading-[100%] tracking-[-0.04em] cursor-pointer"
              onClick={() => {
                setSelectedItem(data);
                setSummarydata(summary);
              }}
            >
              View More
            </span>
          </div>
        </div>
      </div>

      {/* Keywords and Important Items Section */}
      <div className="w-[100%] lg:w-[46%] border-x-[0.5px] lg:border-s-0 border-[#9E9E9E]">
        {/* Keywords Section */}
        <div className="relative text-black px-5 py-6 flex flex-col gap-3 border-b-[0.5px] border-[#9E9E9E]">
          <h1 className="font-semibold text-[9px] leading-[100%] tracking-[-0.04em]">
            Keywords
          </h1>
          <div className="flex flex-wrap gap-2 w-full sm:w-[85%]">
            {Keywords?.slice(0, 3).map((item: string, i: number) => (
              <span
                key={i}
                className="px-5 bg-[#353535] text-white py-2 rounded-[22px] font-black text-[6px] leading-[100%] tracking-[-0.04em]"
              >
                {item}
              </span>
            ))}
          </div>
          <span
            className="w-fit px-2.5 bg-[#353535] text-white py-1 rounded-[8.5px] font-medium text-[5px] leading-[100%] tracking-[-0.04em] absolute right-2.5 bottom-2.5 cursor-pointer"
            onClick={() => {
              setSelectedItem(data);
              setKeywords(Keywords);
            }}
          >
            View More
          </span>
        </div>

        {/* Important Items Section */}
        <div className="relative text-black px-5 py-6 flex flex-col gap-3">
          <h1 className="font-semibold text-[9px] leading-[100%] tracking-[-0.04em]">
            Important Items
          </h1>
          {Important_items?.slice(0, 2).map((item: string, index: number) => (
            <div className="flex gap-2.5" key={index}>
              <img src={infoIcon} alt="icon" />
              <p className="font-medium text-[8px] leading-[198%] tracking-[-0.04em] text-[#222222] truncate-item">
                {item}
              </p>
            </div>
          ))}
          <span
            className="w-fit px-2.5 bg-[#353535] text-white py-1 rounded-[8.5px] font-medium text-[5px] leading-[100%] tracking-[-0.04em] absolute right-2.5 bottom-2.5 cursor-pointer"
            onClick={() => {
              setSelectedItem(data);
              setImpItems(Important_items);
            }}
          >
            View More
          </span>
        </div>
      </div>

      {/* Modal for expanded content */}
      <Modal
        title={summaryData ? "Summary" : impItems.length ? "Important Items" : "Keywords"}
        open={!!selectedItem}
        onCancel={() => setSelectedItem(null)}
        footer={null}
        width={800}
        styles={{
          body: { maxHeight: "70vh", overflowY: "auto", fontFamily: "Inter" },
        }}
      >
        <div className="py-5">
          {summaryData ? (
            summaryData
          ) : impItems.length ? (
            impItems.map((item: string, index: number) => (
              <div className="flex gap-2.5 mb-2" key={index}>
                <img src={infoIcon} alt="icon" />
                <p className="font-medium text-[8px] leading-[198%] tracking-[-0.04em] text-[#222222]">
                  {item}
                </p>
              </div>
            ))
          ) : (
            keywords.map((keyword: string, i: number) => (
              <span
                key={i}
                className="px-5 bg-[#353535] text-white py-2 rounded-[22px] font-black text-[6px] leading-[100%] tracking-[-0.04em] ml-4"
              >
                {keyword}
              </span>
            ))
          )}
        </div>
      </Modal>
    </div>
  );
};
```

## State Management & Data Flow

### Context Providers
```typescript
// User Context
interface UserContextType {
  user: User | null;
  setUser: (user: User | null) => void;
  loadUserFromStorage: () => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);

  const loadUserFromStorage = useCallback(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
  }, []);

  return (
    <UserContext.Provider value={{ user, setUser, loadUserFromStorage }}>
      {children}
    </UserContext.Provider>
  );
};

// Loader Context
interface LoaderContextType {
  loading: boolean;
  setLoading: (loading: boolean) => void;
}

const LoaderContext = createContext<LoaderContextType | undefined>(undefined);

export const LoaderProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [loading, setLoading] = useState(false);

  return (
    <LoaderContext.Provider value={{ loading, setLoading }}>
      {children}
    </LoaderContext.Provider>
  );
};
```

### Custom Hooks
```typescript
// Window Resize Hook
const useWindowResize = () => {
  const [size, setSize] = useState({
    screenWidth: window.innerWidth,
    screenHeight: window.innerHeight,
  });

  useLayoutEffect(() => {
    const updateSize = () => {
      setSize({
        screenWidth: window.innerWidth,
        screenHeight: window.innerHeight
      });
    };

    window.addEventListener("resize", updateSize);
    updateSize();

    return () => window.removeEventListener("resize", updateSize);
  }, []);

  return size;
};

// User Hook
const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

// Loader Hook
const useLoader = () => {
  const context = useContext(LoaderContext);
  if (context === undefined) {
    throw new Error('useLoader must be used within a LoaderProvider');
  }
  return context;
};
```

## API Integration Patterns

### Service Layer Architecture
```typescript
// Axios Instance Configuration
const BASE_URL = "https://backend.mergenai.io/api/v1";

export const ProtectedAPI: AxiosInstance = axios.create({
  baseURL: BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

ProtectedAPI.interceptors.request.use((config) => {
  const token = localStorage.getItem("authToken");
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Message Service
interface CreateMessagePayload {
  chatId: string;
  content: string;
  sender: 'user' | 'ai';
  attachedFiles: {
    image?: File[];
    audio?: File[];
    url?: string[];
    file?: File[];
  };
}

const createMessage = async (payload: CreateMessagePayload) => {
  const formData = new FormData();

  formData.append("chatId", payload.chatId);
  formData.append("content", payload.content);
  formData.append("sender", payload.sender);
  formData.append("isDeepResearch", JSON.stringify(false));

  // Append files
  payload.attachedFiles.image?.forEach((file) => {
    formData.append("image[]", file);
  });

  payload.attachedFiles.audio?.forEach((file) => {
    formData.append("audio[]", file);
  });

  payload.attachedFiles.url?.forEach((url) => {
    formData.append("url[]", url);
  });

  payload.attachedFiles.file?.forEach((file) => {
    formData.append("file[]", file);
  });

  const response = await ProtectedAPI.post("/messages", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    }
  });

  return response.data;
};

// Conversation Service
const createConversation = async ({ title }: { title: string }) => {
  const response = await ProtectedAPI.post("/conversations", { title });
  return response.data;
};

const getConversation = async () => {
  const response = await ProtectedAPI.get("/conversations?isDeepResearch=false");
  return response.data;
};

// Reference Service
const createReference = async ({ messageId, content }: { messageId: string; content: any }) => {
  const response = await ProtectedAPI.post("/references", { messageId, content });
  return response.data;
};

const getReferencesByMessageId = async (messageId: string) => {
  const response = await ProtectedAPI.get(`/references/message/${messageId}`);
  return response.data;
};
```

## Styling & Animation System

### Glass Morphism Implementation
```css
/* Glass effect for drawer and modals */
.glass-effect {
  background: linear-gradient(168.12deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0.1) 98.85%);
  backdrop-filter: blur(40px);
  box-shadow: 0px 4px 24px -1px rgba(136,136,136,0.12);
}

/* Background drawer with image overlay */
.bg-drawer {
  background-image: url("./assets/images/historyDrawerBg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
```

### Text Truncation System
```css
/* Multi-line truncation for summaries */
.truncate-text {
  display: -webkit-box;
  -webkit-line-clamp: 8;
  line-clamp: 8;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

/* Single line truncation for items */
.truncate-item {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

/* Hide textarea scrollbar */
.textarea-hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.textarea-hide-scrollbar::-webkit-scrollbar {
  display: none;
}
```

### Custom Scrollbar Styling
```css
.scrollbar-custom::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-custom::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.5);
}
```

### Framer Motion Animations
```typescript
// Drawer slide animation
const drawerVariants = {
  hidden: { x: "-100%", width: 0, opacity: 0 },
  visible: { x: 0, width: "279px", opacity: 1 },
  exit: { x: "-100%", width: 0, opacity: 0 }
};

// Card entrance animation
const cardVariants = {
  hidden: { x: 0, opacity: 0 },
  visible: { x: 0, opacity: 1 },
  exit: { x: 0, opacity: 0 }
};

// Usage in components
<motion.div
  initial="hidden"
  animate="visible"
  exit="exit"
  variants={drawerVariants}
  transition={{ duration: 0.6, ease: "easeInOut" }}
>
  {/* Content */}
</motion.div>
```

## Performance & Optimization

### Vite Configuration
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [react()],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'react-vendor': ['react', 'react-dom'],
          'antd-vendor': ['antd'],
          'quill-vendor': ['react-quill'],
          'motion-vendor': ['framer-motion']
        }
      }
    }
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'antd', 'framer-motion']
  }
});
```

### Code Splitting Strategy
```typescript
// Lazy loading for heavy components
const EmailSection = lazy(() => import('./emailSection/EmailSection'));
const ReportSection = lazy(() => import('../report/ReportSection'));

// Usage with Suspense
<Suspense fallback={<div>Loading...</div>}>
  <EmailSection />
</Suspense>
```

### Memory Management
```typescript
// Cleanup in useEffect
useEffect(() => {
  const handleResize = () => setSize(getWindowSize());
  window.addEventListener('resize', handleResize);

  return () => {
    window.removeEventListener('resize', handleResize);
  };
}, []);

// Debounced input handling
const debouncedSearch = useMemo(
  () => debounce((value: string) => {
    // Perform search
  }, 300),
  []
);
```

## Implementation Checklist

### 1. Project Setup
- [ ] Initialize React + TypeScript + Vite project
- [ ] Install dependencies: Tailwind CSS, Framer Motion, Ant Design, Axios
- [ ] Configure Tailwind CSS with custom fonts and utilities
- [ ] Set up folder structure with components, services, hooks, context

### 2. Core Components
- [ ] Implement SearchBar with auto-resize and file upload
- [ ] Create Dashboard with state management and routing
- [ ] Build ListCards with modal system and truncation
- [ ] Develop HistoryDrawer with glass morphism effects
- [ ] Set up FooterSection with message navigation

### 3. State Management
- [ ] Create UserContext and LoaderContext providers
- [ ] Implement custom hooks (useUser, useLoader, useWindowResize)
- [ ] Set up local state management patterns
- [ ] Configure error handling and loading states

### 4. API Integration
- [ ] Set up Axios instance with interceptors
- [ ] Create service layer for messages, conversations, references
- [ ] Implement FormData handling for file uploads
- [ ] Configure authentication and error handling

### 5. Styling System
- [ ] Import custom fonts (Inter, Charter, Tw-Cen-MT)
- [ ] Implement glass morphism and backdrop blur effects
- [ ] Set up text truncation utilities
- [ ] Configure custom scrollbar styling
- [ ] Add responsive breakpoints and grid system

### 6. Animation & Interactions
- [ ] Implement Framer Motion page transitions
- [ ] Add hover effects and micro-interactions
- [ ] Set up modal animations and state transitions
- [ ] Configure drawer slide animations

### 7. Performance Optimization
- [ ] Configure Vite build optimization
- [ ] Implement code splitting for vendor libraries
- [ ] Add lazy loading for heavy components
- [ ] Set up proper cleanup and memory management

### 8. Testing & Deployment
- [ ] Write unit tests for core components
- [ ] Test responsive behavior across devices
- [ ] Validate API integration and error handling
- [ ] Configure production build and deployment

This detailed implementation guide provides comprehensive coverage of the Mergen AI dashboard architecture, enabling developers to replicate similar functionality with modern React patterns and best practices.

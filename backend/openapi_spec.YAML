openapi: 3.0.3
info:
  title: Mergen AI API
  description: |
    API for Mergen AI platform that enables users to register, login, and interact with the AI through
    message exchanges, references, chats, and conversations.
  version: 1.0.0
  contact:
    name: Mergen AI Support
    email: <EMAIL>

servers:
  - url: 'https://api.mergenai.io/v1'
    description: Production server
  - url: 'http://localhost:5173'
    description: Development server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    User:
      type: object
      required:
        - id
        - username
        - email
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the user
        username:
          type: string
          description: Username for login
        email:
          type: string
          format: email
          description: User's email address
        firstName:
          type: string
          description: User's first name
        lastName:
          type: string
          description: User's last name
        createdAt:
          type: string
          format: date-time
          description: Timestamp when user was created
        updatedAt:
          type: string
          format: date-time
          description: Timestamp when user was last updated

    UserRegistration:
      type: object
      required:
        - username
        - email
        - password
      properties:
        username:
          type: string
          description: Unique username for login
        email:
          type: string
          format: email
          description: User's email address
        password:
          type: string
          format: password
          description: User's password
        firstName:
          type: string
          description: User's first name
        lastName:
          type: string
          description: User's last name

    LoginRequest:
      type: object
      required:
        - username
        - password
      properties:
        username:
          type: string
          description: User's username
        password:
          type: string
          format: password
          description: User's password

    LoginResponse:
      type: object
      required:
        - token
        - user
      properties:
        token:
          type: string
          description: JWT token for authentication
        user:
          $ref: '#/components/schemas/User'

    ChangePasswordRequest:
      type: object
      required:
        - currentPassword
        - newPassword
      properties:
        currentPassword:
          type: string
          format: password
          description: User's current password
        newPassword:
          type: string
          format: password
          description: User's new password

    Reference:
      type: object
      required:
        - id
        - type
        - messageId
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the reference
        messageId:
          type: string
          format: uuid
          description: ID of the message this reference belongs to
        type:
          type: string
          enum: [document_set, document, document_piece]
          description: Type of the reference
        content:
          type: string
          description: Reference content
        metadata:
          type: object
          description: Additional metadata about the reference
        sourceUrl:
          type: string
          format: uri
          description: URL to the source if applicable
        createdAt:
          type: string
          format: date-time
          description: Timestamp when reference was created

    CreateReferenceRequest:
      type: object
      required:
        - type
        - messageId
      properties:
        messageId:
          type: string
          format: uuid
          description: ID of the message this reference belongs to
        type:
          type: string
          enum: [document_set, document, document_piece]
          description: Type of the reference
        content:
          type: string
          description: Reference content
        metadata:
          type: object
          description: Additional metadata about the reference
        sourceUrl:
          type: string
          format: uri
          description: URL to the source if applicable

    Message:
      type: object
      required:
        - id
        - chatId
        - content
        - sender
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the message
        chatId:
          type: string
          format: uuid
          description: ID of the chat this message belongs to
        content:
          type: string
          description: Content of the message
        sender:
          type: string
          enum: [user, ai]
          description: Sender of the message
        timestamp:
          type: string
          format: date-time
          description: Timestamp when message was sent
        references:
          type: array
          items:
            $ref: '#/components/schemas/Reference'
          description: References associated with this message
        metadata:
          type: object
          description: Additional metadata about the message

    CreateMessageRequest:
      type: object
      required:
        - chatId
        - content
        - sender
      properties:
        chatId:
          type: string
          format: uuid
          description: ID of the chat this message belongs to
        content:
          type: string
          description: Content of the message
        sender:
          type: string
          enum: [user, ai]
          description: Sender of the message
        metadata:
          type: object
          description: Additional metadata about the message

    Chat:
      type: object
      required:
        - id
        - conversationId
        - title
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the chat
        conversationId:
          type: string
          format: uuid
          description: ID of the conversation this chat belongs to
        title:
          type: string
          description: Title of the chat session
        createdAt:
          type: string
          format: date-time
          description: Timestamp when chat was created
        updatedAt:
          type: string
          format: date-time
          description: Timestamp when chat was last updated
        summary:
          type: string
          description: Summary of the chat conversation
        keywords:
          type: array
          items:
            type: string
          description: Keywords associated with this chat
        messages:
          type: array
          items:
            $ref: '#/components/schemas/Message'
          description: Messages in this chat

    CreateChatRequest:
      type: object
      required:
        - conversationId
        - title
      properties:
        conversationId:
          type: string
          format: uuid
          description: ID of the conversation this chat belongs to
        title:
          type: string
          description: Title of the chat session
        summary:
          type: string
          description: Summary of the chat conversation
        keywords:
          type: array
          items:
            type: string
          description: Keywords associated with this chat

    UpdateChatRequest:
      type: object
      properties:
        title:
          type: string
          description: Updated title of the chat session
        summary:
          type: string
          description: Updated summary of the chat conversation
        keywords:
          type: array
          items:
            type: string
          description: Updated keywords associated with this chat

    Conversation:
      type: object
      required:
        - id
        - userId
        - title
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the conversation
        userId:
          type: string
          format: uuid
          description: ID of the user who owns this conversation
        title:
          type: string
          description: Title of the conversation
        createdAt:
          type: string
          format: date-time
          description: Timestamp when conversation was created
        updatedAt:
          type: string
          format: date-time
          description: Timestamp when conversation was last updated
        chats:
          type: array
          items:
            $ref: '#/components/schemas/Chat'
          description: Chats in this conversation

    CreateConversationRequest:
      type: object
      required:
        - title
      properties:
        title:
          type: string
          description: Title of the conversation

    UpdateConversationRequest:
      type: object
      properties:
        title:
          type: string
          description: Updated title of the conversation

    ErrorResponse:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: integer
          description: HTTP status code
        message:
          type: string
          description: Error message
        details:
          type: object
          description: Additional error details

paths:
  /users/register:
    post:
      summary: Register a new user
      description: Creates a new user account with username and password
      operationId: registerUser
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRegistration'
            example:
              username: johndoe
              email: <EMAIL>
              password: securePassword123
              firstName: John
              lastName: Doe
      responses:
        '201':
          description: User successfully registered
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
              example:
                id: 123e4567-e89b-12d3-a456-************
                username: johndoe
                email: <EMAIL>
                firstName: John
                lastName: Doe
                createdAt: "2023-01-15T08:30:00Z"
                updatedAt: "2023-01-15T08:30:00Z"
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 400
                message: "Username already exists"
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /users/login:
    post:
      summary: Login user
      description: Authenticates a user and returns a JWT token
      operationId: loginUser
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
            example:
              username: johndoe
              password: securePassword123
      responses:
        '200':
          description: Successful login
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
              example:
                token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                user:
                  id: 123e4567-e89b-12d3-a456-************
                  username: johndoe
                  email: <EMAIL>
                  firstName: John
                  lastName: Doe
                  createdAt: "2023-01-15T08:30:00Z"
                  updatedAt: "2023-01-15T08:30:00Z"
        '401':
          description: Authentication failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 401
                message: "Invalid username or password"
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /users/logout:
    post:
      summary: Logout user
      description: Invalidates the current JWT token
      operationId: logoutUser
      tags:
        - Authentication
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Successfully logged out
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /users/change-password:
    patch:
      summary: Change user password
      description: Updates the user's password
      operationId: changePassword
      tags:
        - Authentication
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
            example:
              currentPassword: oldPassword123
              newPassword: newSecurePassword456
      responses:
        '200':
          description: Password successfully changed
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 400
                message: "New password does not meet requirements"
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 401
                message: "Current password is incorrect"
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /users/{userId}:
    get:
      summary: Get user profile
      description: Retrieves information about a specific user
      operationId: getUser
      tags:
        - Users
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the user to retrieve
      responses:
        '200':
          description: User found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /messages:
    post:
      summary: Create a new message
      description: Creates a new message in a specific chat
      operationId: createMessage
      tags:
        - Messages
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMessageRequest'
            example:
              chatId: 123e4567-e89b-12d3-a456-426614174001
              content: "Hello, I'd like to learn more about critical minerals deals."
              sender: "user"
      responses:
        '201':
          description: Message created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Message'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /messages/{messageId}:
    get:
      summary: Get a specific message
      description: Retrieves information about a specific message
      operationId: getMessage
      tags:
        - Messages
      security:
        - bearerAuth: []
      parameters:
        - name: messageId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the message to retrieve
      responses:
        '200':
          description: Message found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Message'
        '404':
          description: Message not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      summary: Delete a message
      description: Deletes a specific message
      operationId: deleteMessage
      tags:
        - Messages
      security:
        - bearerAuth: []
      parameters:
        - name: messageId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the message to delete
      responses:
        '204':
          description: Message deleted successfully
        '404':
          description: Message not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    patch:
      summary: Update a message
      description: Updates a specific message
      operationId: updateMessage
      tags:
        - Messages
      security:
        - bearerAuth: []
      parameters:
        - name: messageId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the message to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                content:
                  type: string
                  description: Updated content of the message
            example:
              content: "Updated message content about critical minerals deals."
      responses:
        '200':
          description: Message updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Message'
        '404':
          description: Message not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /messages/chat/{chatId}:
    get:
      summary: Get all messages in a chat
      description: Retrieves all messages in a specific chat
      operationId: getChatMessages
      tags:
        - Messages
      security:
        - bearerAuth: []
      parameters:
        - name: chatId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the chat
      responses:
        '200':
          description: Messages found
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Message'
        '404':
          description: Chat not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /references:
    post:
      summary: Create a new reference
      description: Creates a new reference for a specific message
      operationId: createReference
      tags:
        - References
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateReferenceRequest'
            example:
              messageId: 123e4567-e89b-12d3-a456-426614174002
              type: "document"
              content: "US official demands Zelenskyy return to talks over critical minerals deal"
              sourceUrl: "https://example.com/article/12345"
      responses:
        '201':
          description: Reference created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Reference'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /references/{referenceId}:
    get:
      summary: Get a specific reference
      description: Retrieves information about a specific reference
      operationId: getReference
      tags:
        - References
      security:
        - bearerAuth: []
      parameters:
        - name: referenceId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the reference to retrieve
      responses:
        '200':
          description: Reference found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Reference'
        '404':
          description: Reference not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      summary: Delete a reference
      description: Deletes a specific reference
      operationId: deleteReference
      tags:
        - References
      security:
        - bearerAuth: []
      parameters:
        - name: referenceId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the reference to delete
      responses:
        '204':
          description: Reference deleted successfully
        '404':
          description: Reference not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    patch:
      summary: Update a reference
      description: Updates a specific reference
      operationId: updateReference
      tags:
        - References
      security:
        - bearerAuth: []
      parameters:
        - name: referenceId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the reference to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                content:
                  type: string
                  description: Updated content of the reference
                sourceUrl:
                  type: string
                  format: uri
                  description: Updated URL to the source
            example:
              content: "Updated reference content about critical minerals deal"
              sourceUrl: "https://example.com/updated-article/12345"
      responses:
        '200':
          description: Reference updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Reference'
        '404':
          description: Reference not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /references/message/{messageId}:
    get:
      summary: Get all references for a message
      description: Retrieves all references associated with a specific message
      operationId: getMessageReferences
      tags:
        - References
      security:
        - bearerAuth: []
      parameters:
        - name: messageId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the message
      responses:
        '200':
          description: References found
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Reference'
        '404':
          description: Message not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /chats:
    post:
      summary: Create a new chat
      description: Creates a new chat in a specific conversation
      operationId: createChat
      tags:
        - Chats
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateChatRequest'
            example:
              conversationId: 123e4567-e89b-12d3-a456-426614174003
              title: "Chat #1"
      responses:
        '201':
          description: Chat created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Chat'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /chats/{chatId}:
    get:
      summary: Get a specific chat
      description: Retrieves information about a specific chat
      operationId: getChat
      tags:
        - Chats
      security:
        - bearerAuth: []
      parameters:
        - name: chatId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the chat to retrieve
      responses:
        '200':
          description: Chat found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Chat'
        '404':
          description: Chat not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      summary: Delete a chat
      description: Deletes a specific chat
      operationId: deleteChat
      tags:
        - Chats
      security:
        - bearerAuth: []
      parameters:
        - name: chatId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the chat to delete
      responses:
        '204':
          description: Chat deleted successfully
        '404':
          description: Chat not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    patch:
      summary: Update a chat
      description: Updates a specific chat
      operationId: updateChat
      tags:
        - Chats
      security:
        - bearerAuth: []
      parameters:
        - name: chatId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the chat to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateChatRequest'
            example:
              title: "Updated Chat Title"
              summary: "This is an updated summary of the chat"
              keywords: ["minerals", "negotiations", "Ukraine"]
      responses:
        '200':
          description: Chat updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Chat'
        '404':
          description: Chat not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /chats/conversation/{conversationId}:
    get:
      summary: Get all chats in a conversation
      description: Retrieves all chats in a specific conversation
      operationId: getConversationChats
      tags:
        - Chats
      security:
        - bearerAuth: []
      parameters:
        - name: conversationId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the conversation
      responses:
        '200':
          description: Chats found
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Chat'
        '404':
          description: Conversation not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /conversations:
    post:
      summary: Create a new conversation
      description: Creates a new conversation for the authenticated user
      operationId: createConversation
      tags:
        - Conversations
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateConversationRequest'
            example:
              title: "Conversation about critical minerals"
      responses:
        '201':
          description: Conversation created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Conversation'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    get:
      summary: Get all conversations
      description: Retrieves all conversations for the authenticated user
      operationId: getConversations
      tags:
        - Conversations
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Conversations found
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Conversation'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /conversations/{conversationId}:
    get:
      summary: Get a specific conversation
      description: Retrieves information about a specific conversation
      operationId: getConversation
      tags:
        - Conversations
      security:
        - bearerAuth: []
      parameters:
        - name: conversationId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the conversation to retrieve
      responses:
        '200':
          description: Conversation found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Conversation'
        '404':
          description: Conversation not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized

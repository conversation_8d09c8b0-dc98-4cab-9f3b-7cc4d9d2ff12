const jwt = require("jsonwebtoken");
const UserToken = require("../models/UserToken");

exports.verifyToken = async (req, res, next) => {
    const token = req.headers.authorization?.split(" ")[1];
    if (!token) return res.status(401).json({ error: "Unauthorized" });

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        const jtiExists = await UserToken.findOne({ jti: decoded.jti }).populate("userId").exec();
        if (!jtiExists) return res.status(401).json({ error: "Token revoked or expired" });

        req.user = decoded;
        req.token = token
        next();
    } catch (err) {
        console.log(err, "errorororro");
        return res.status(401).json({ error: err.message });
    }
}

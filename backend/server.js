const express = require('express');
const dotenv = require('dotenv');
const cors = require("cors")
const morgan = require('morgan');
const fs = require('fs');
const path = require('path');
let https = require("http");
const { Server } = require("socket.io");

const initRoutes = require('./routes');
const stripeWebhookHandler = require('./controllers/Webhooks');
const connectDB = require('./config/db');
const host = "************"


dotenv.config();

const app = express();
let server = https.createServer(app);
const io = new Server(server, {
  path: "/sockets",
  cors: {
    origin: "*",
    methods: ["GET", "POST", "GET", "PUT", "DELETE", "PATCH"],
  },
})
app.use(cors())
app.post('/api/v1/webhooks/stripePayments',
  express.raw({ type: 'application/json' }),
  stripeWebhookHandler.updatePayments);

const accessLogStream = fs.createWriteStream(path.join(__dirname, 'access.log'), { flags: 'a' });
app.use(morgan('combined', { stream: accessLogStream }));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.get("/", (req, res, next) => res.send("Welcome to the mergen AI"))
app.use('/api/v1', initRoutes(io));
app.use((err, req, res, next) => {
  console.error(err.stack);
  console.error(err, "errror in error middleware");
  const statusCode = err.status || 500;
  const message = err.message || String(err) || 'Unknown error';
  res.status(statusCode).json({
    success: false,
    message,
  });
})
const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  connectDB();
  console.log(`Server ready at http://localhost:${PORT}`);
});

io.on("connection", (socket) => {
  console.log(socket.id, "connected successfully");
  socket.on("disconnect", () => {
    console.log("A user disconnected");
  });
});
const express = require("express");
const AWS = require("aws-sdk");
const { config } = require("dotenv");
config();
const {
  getImages,
  deleteImage,
  uploadFile,
  getAllImages,
} = require("../controllers/Images");
const { verifyToken } = require("../middlewares/auth");
const upload = require("../utils/multer")
const router = express.Router();

// Routes
router.post("/upload", verifyToken, upload.single("file"), uploadFile);
router.get("/getImages", verifyToken, getAllImages);
router.delete("/delete", verifyToken, deleteImage);

module.exports = router;
exports.MulterStorage = upload;
const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require("uuid");
const User = require('../models/Users.js');
const UserToken = require('../models/UserToken');
const { verifyToken } = require('../middlewares/auth');
const router = express.Router();
const { registerUser, changePassword, loginUser, logoutUser, forgetPassword, updatePassword, updateProfile, verifyUser, getAllUsers, userVerification } = require('../controllers/Users.js');

router.post('/register', registerUser);
router.post('/login', loginUser);
router.post("/logout", verifyToken, logoutUser)
router.post('/change-password', changePassword);
router.post('/forget-password', forgetPassword);
router.put('/update-password', verifyToken, updatePassword);
router.put('/update-profile', verifyToken, updateProfile);
router.get('/isAuth', verifyToken, verifyUser);
router.get('/getAllUsers', verifyToken, getAllUsers);
router.get('/verify', userVerification);

module.exports = router; 
const express = require('express');
const router = express.Router();
const { createQuotes, deleteQuotes, getAllQuotes, getQuoteById, updateQuoteById } = require('../controllers/Quotes');
const { verifyToken } = require("../middlewares/auth")

router.get('/', verifyToken, getAllQuotes);
router.post('/', verifyToken, createQuotes);
router.get('/:quoteId', verifyToken, getQuoteById);
router.delete('/:quoteId', verifyToken, deleteQuotes);
router.put('/:quoteId', verifyToken, updateQuoteById);

module.exports = router;
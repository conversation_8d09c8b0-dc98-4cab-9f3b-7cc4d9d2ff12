const express = require('express');
const router = express.Router();
const { createMessage, getMessagesByChatId, getMessageById, updateMessage, deleteMessageById } = require('../controllers/Messages');
const { verifyToken } = require("../middlewares/auth")
const upload = require("../utils/multer")

router.post('/', verifyToken, upload.any(), createMessage);
router.get('/chat/:chatId', verifyToken, getMessagesByChatId);
router.get('/:messageId', verifyToken, getMessageById);
router.patch('/:messageId', verifyToken, updateMessage);
router.delete('/:messageId', verifyToken, deleteMessageById);

module.exports = router;
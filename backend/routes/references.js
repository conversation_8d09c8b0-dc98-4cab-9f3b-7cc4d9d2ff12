const express = require('express');
const router = express.Router();
const { createReference, updateReference, deleteReferenceById, getReferenceById, getReferenceByMessageId } = require("../controllers/References")
const { verifyToken } = require("../middlewares/auth")

router.post('/', verifyToken, createReference);
router.get('/message/:messageId', verifyToken, getReferenceByMessageId);
router.get('/:referenceId', verifyToken, getReferenceById);
router.patch('/:referenceId', verifyToken, updateReference);
router.delete('/:referenceId', verifyToken, deleteReferenceById);

module.exports = router;
const express = require('express');
const router = express.Router();
const { createChat, updateChat, deleteChat, getChatById, getChatsByConversationId } = require('../controllers/Chats.js');
const { verifyToken } = require("../middlewares/auth")
router.post('/', verifyToken, createChat);
router.get('/conversation/:conversationId', verifyToken, getChatsByConversationId);
router.get('/:chatId', verifyToken, getChatById);
router.patch('/:chatId', verifyToken, updateChat);
router.delete('/:chatId', verifyToken, deleteChat);

module.exports = router;
const express = require('express');
const router = express.Router();
const goalsController = require('../controllers/Goals');
const { verifyToken } = require('../middlewares/auth');

router.get('/', verifyToken, goalsController.getAllGoals);

router.get('/:goalId', verifyToken, goalsController.getGoalById);

router.post('/', verifyToken, goalsController.createGoal);

router.put('/:goalId', verifyToken, goalsController.updateGoalById);

router.delete('/:goalId', verifyToken, goalsController.deleteGoal);

module.exports = router;

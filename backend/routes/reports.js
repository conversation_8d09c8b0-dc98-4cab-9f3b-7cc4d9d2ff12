const express = require('express');
const router = express.Router();
const reportsController = require('../controllers/Reports');
const { verifyToken } = require('../middlewares/auth');

router.post('/', verifyToken, reportsController.createReport);
router.post('/createEditReport', verifyToken, reportsController.createEditReport);
router.put('/updateReport', verifyToken, reportsController.updateEditedReport);
module.exports = router;

const express = require('express');
const router = express.Router();
const { createConversation, getConversations, getConversationsById, updateConversationById, deleteConversationById } = require('../controllers/Conversations');
const { verifyToken } = require("../middlewares/auth")

router.post('/', verifyToken, createConversation);
router.get('/', verifyToken, getConversations);
router.get('/:conversationId', verifyToken, getConversationsById);
router.put('/:conversationId', verifyToken, updateConversationById);
router.delete('/:conversationId', verifyToken, deleteConversationById);
module.exports = router;
const express = require('express');
const router = express.Router();
const { createNotifications, updateNotifications, getNotifications } = require('../controllers/Notifications');
const { verifyToken } = require("../middlewares/auth")

const initNotificationRoutes = (io) => {
    router.get('/', verifyToken, getNotifications);
    router.post('/', verifyToken, createNotifications(io));
    router.put('/:id', verifyToken, updateNotifications);
    return router
}
module.exports = initNotificationRoutes;
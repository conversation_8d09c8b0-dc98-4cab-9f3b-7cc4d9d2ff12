const express = require("express")
const Router = express.Router()
const userRoutes = require("./users")
const messagesRoutes = require("./messages")
const chatsRoutes = require("./chats")
const referencesRoutes = require("./references.js")
const conversationsRoutes = require("./conversations")
const todoRoutes = require('./todoRoutes')
const mergenTodoRoutes = require('./mergenTodo.js')
const newsRoutes = require('./news.js')
const imageRoutes = require('./Images.js')
const quoteRoutes = require("./quotes.js")
const goalRoutes = require("./goals.js")
const reportRoutes = require("./reports.js")
const subsctriptionRoutes = require("./subscriptions.js")
const productRoutes = require("./products.js")
const priceRoutes = require("./prices.js")
const webhookRoutes = require("./webhooks.js")
const initNotificationRoutes = require("./notifications.js")
const rfpRoutes = require("./rfp.js")
const legalResearchRoutes = require("./legalResearch.js")

const initRoutes = (io) => {
    Router.use("/users", userRoutes)
    Router.use("/messages", messagesRoutes)
    Router.use("/chats", chatsRoutes)
    Router.use("/references", referencesRoutes)
    Router.use("/conversations", conversationsRoutes)
    Router.use('/todos', todoRoutes)
    Router.use('/mergen-todos', mergenTodoRoutes)
    Router.use('/news', newsRoutes)
    Router.use('/image', imageRoutes)
    Router.use('/quotes', quoteRoutes)
    Router.use('/goals', goalRoutes)
    Router.use('/reports', reportRoutes)
    Router.use('/rfp', rfpRoutes)
    Router.use('/legalResearch', legalResearchRoutes)
    Router.use('/products', productRoutes)
    Router.use('/prices', priceRoutes)
    Router.use('/subscriptions', subsctriptionRoutes)
    Router.use('/webhooks', webhookRoutes)
    Router.use('/notifications', initNotificationRoutes(io))
    return Router
}

module.exports = initRoutes
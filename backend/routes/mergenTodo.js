const express = require('express');
const router = express.Router();
const {
    getTodos,
    getTodo,
    createTodo,
    updateTodo,
    deleteTodo
} = require('../controllers/MergenTodo');
const { verifyToken } = require("../middlewares/auth")

router
    .route('/')
    .get(verifyToken, getTodos)
    .post(verifyToken, createTodo);

router
    .route('/:id')
    .get(verifyToken, getTodo)
    .put(verifyToken, updateTodo)
    .delete(verifyToken, deleteTodo);

module.exports = router; 
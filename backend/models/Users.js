const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
    username: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    resetToken: { type: String, default: "" },
    expireTokenIn: { type: Number },
    goals: [{ type: mongoose.Types.ObjectId, ref: 'Goals' }],
    stripeCustomerId: { type: String },
    subscriptions: [{ type: mongoose.Types.ObjectId, ref: 'Subscription' }],
    isEmailVerified:{type:Boolean,default:false}

}, { timestamps: true });

module.exports = mongoose.model('User', userSchema); 
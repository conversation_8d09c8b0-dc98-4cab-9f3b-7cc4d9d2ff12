const mongoose = require('mongoose');

const subscriptionSchema = new mongoose.Schema({
  userId: { type: mongoose.Types.ObjectId, ref: 'User', required: true },

  subscriptionId: { type: String, required: true, unique: true },
  customerId: { type: String, required: true },
  checkoutSessionId: { type: String },
  invoiceId: { type: String },
  priceId: { type: String, required: true },
  productId: { type: String, required: true },

  currency: { type: String },
  amount: { type: Number },

  paymentStatus: {
    type: String,
    enum: ['paid', 'unpaid', 'no_payment_required'],
    required: true,
  },

  status: {
    type: String,
    enum: [
      'active',
      'trialing',
      'past_due',
      'canceled',
      'incomplete',
      'incomplete_expired',
      'unpaid',
    ],
    required: true,
  },
  cancelAtPeriodEnd: { type: Boolean },
  currentPeriodStart: { type: Date },
  currentPeriodEnd: { type: Date },
}, {
  timestamps: true
});

module.exports = mongoose.model('Subscription', subscriptionSchema);

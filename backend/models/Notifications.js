const mongoose = require('mongoose');

const notificationsSchema = new mongoose.Schema(
    {
        title: { type: String, default: "" },
        description: { type: String, default: "" },
        type: { type: String, default: "" },
        user: { type: mongoose.Types.ObjectId, ref: 'User' },
        conversation: { type: mongoose.Types.ObjectId, ref: 'Conversation' },
        chat: { type: mongoose.Types.ObjectId, ref: 'Chat' },
        isRead: { type: Boolean, default: false },
    },
    { timestamps: true }
);

module.exports = mongoose.model('Notifications', notificationsSchema); 
const mongoose = require('mongoose');

const editReportSchema = new mongoose.Schema({
    reportId: {
        type: mongoose.Types.ObjectId,
        ref: 'Reports'
    },
    content: {
        type: String,
        required: true,
    },
    sender: {
        type: String,
        enum: ['user', 'ai'],
        required: true,
    },
    metadata: {
        type: Object,
    },
},
    {
        timestamps: true
    });

module.exports = mongoose.model('editReport', editReportSchema);

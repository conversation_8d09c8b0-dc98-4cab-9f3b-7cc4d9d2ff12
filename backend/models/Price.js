const mongoose = require('mongoose');

const priceSchema = new mongoose.Schema({
  priceId: { type: String, required: true, unique: true },  // Stripe Price ID (price_xxx)
  productId: { type: String, required: true },              // Stripe Product ID (prod_xxx), links to your Product schema
  unitAmount: { type: Number, required: true },             // Price in cents
  currency: { type: String, default: 'cad' },
  interval: { type: String, enum: ['month', 'year'], required: true },  // Billing interval
  active: { type: Boolean, default: true },                 // Is this price active for new subscribers?
  createdAt: { type: Date, default: Date.now },
});

module.exports = mongoose.model('Price', priceSchema);

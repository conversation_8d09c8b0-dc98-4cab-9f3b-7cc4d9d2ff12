const mongoose = require('mongoose');

const MessageSchema = new mongoose.Schema({
  chatId: {
    type: mongoose.Types.ObjectId,
    ref: 'Chat'
  },
  conversationId: {
     type: mongoose.Types.ObjectId, 
     ref: 'Conversation'  
  },
  content: {
    type: String,
    required: true,
  },
  sender: {
    type: String,
    enum: ['user', 'ai'],
    required: true,
  },
  metadata: {
    type: Object,
  },
},
  {
    timestamps: true
  });

module.exports = mongoose.model('Message', MessageSchema);

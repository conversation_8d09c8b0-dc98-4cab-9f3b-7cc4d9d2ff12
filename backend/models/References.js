const mongoose = require('mongoose');

const ReferenceSchema = new mongoose.Schema({
  messageId: {
    type: mongoose.Types.ObjectId,
    ref: 'Message'
  },
  type: {
    type: String,
    enum: ['document_set', 'document', 'document_piece'],
    // required: true,
  },
  content: {
    type: [],
  },
  metadata: {
    type: Object,
  },
  sourceUrl: {
    type: String,
  }
},
{
  timestamps: true
});

module.exports = mongoose.model('Reference', ReferenceSchema);

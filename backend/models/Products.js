const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  productId: { type: String, required: true, unique: true },  // Stripe Product ID (prod_xxx)
  name: { type: String, required: true },                     // Product name (e.g., "Pro Subscription")
  description: { type: String },                               // Optional description
  images: [String],                                            // Optional array of image URLs
  active: { type: Boolean, default: true },                   // Whether product is active in Stripe
  metadata: { type: mongoose.Schema.Types.Mixed },            // Optional: Stripe metadata object
  createdAt: { type: Date, default: Date.now },
});

module.exports = mongoose.model('Product', productSchema);

const mongoose = require('mongoose');

const ConversationSchema = new mongoose.Schema({
    type: { type: String },
    conversationId: {
        type: mongoose.Types.ObjectId,
        ref: "Conversation",
    },
    success: {
        type: Boolean,
    },
    title: {
        type: String,
        default: "",
    },
    subtitle: {
        type: String,
        default: "",
    },
    versionNumber: {
        type: Number,
    },
    sections: [
        {
            section_name: { type: String, default: "" },
            section_content: { type: String, default: "" }
        }
    ],
    reportId: {
        type: mongoose.Types.ObjectId,
        ref: "Reports",
    },
}, {
    timestamps: true
});

module.exports = mongoose.model('Reports', ConversationSchema);
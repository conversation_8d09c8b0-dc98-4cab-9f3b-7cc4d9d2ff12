const mongoose = require('mongoose');

const deepResearchUsageSchema = new mongoose.Schema({
  userId: { type: mongoose.Types.ObjectId, ref: 'User', required: true },
  date: { type: Date },
  usedAt: { type: Date },
  subscriptionId: { type: String },
  
  // dailyCount: { type: Number, default: 0 },
  // lastHourWindow: { type: Date },
  // hourlyCount: { type: Number, default: 0 },
}, {
  timestamps: true
});

module.exports = mongoose.model('DeepResearchUsage', deepResearchUsageSchema);
const {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
  ListObjectsV2Command,
} = require("@aws-sdk/client-s3");
const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});
async function uploadToS3(stream, key, mimetype) {
  const params = {
    Bucket: process.env.AWS_BUCKET_NAME,
    Key: key,
    Body: stream,
    ContentDisposition: "inline",
  };
  if (mimetype) params.ContentType = mimetype;
  const command = new PutObjectCommand(params);
  return s3Client.send(command);
}

async function getAllS3Objects() {
  const params = {
    Bucket: process.env.AWS_BUCKET_NAME,
  };
  const command = new ListObjectsV2Command(params);
  const response = await s3Client.send(command);
  const bucket = process.env.AWS_BUCKET_NAME;
  const region = process.env.AWS_REGION;

  return (response.Contents || []).map(file => ({
    url: `https://${bucket}.s3.${region}.amazonaws.com/${encodeURIComponent(file.Key)}`,
    key: file.Key,
    size: file.Size,
    lastModified: file.LastModified,
  }));
}

async function deleteObjectFromS3(key) {
  const params = {
    Bucket: process.env.AWS_BUCKET_NAME,
    Key: key,
  };

  const command = new DeleteObjectCommand(params);
  return s3Client.send(command);
}


module.exports = { uploadToS3, getAllS3Objects, deleteObjectFromS3 };
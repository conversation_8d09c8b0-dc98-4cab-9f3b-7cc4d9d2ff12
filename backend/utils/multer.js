const multer = require("multer");

// const storage = multer.memoryStorage();
// const upload = multer({ storage, limits: { fileSize: 10 * 1024 * 1024 } });
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 10 * 1024 * 1024 },
  fileFilter: (req, file, cb) => {
    if (file.size > 10 * 1024 * 1024) {
      return cb(new Error('File size exceeds the 10MB limit'), false);
    }
    cb(null, true);
  }
});

module.exports = upload;
const { RESPONSE_MESSAGES } = require("../messages/responses.js");
const { StatusCodes } = require("../statusCodes/index.js");

exports.sendResponse = (req, res, Code, message, data) => {
    Code = Code || StatusCodes.OK;
    message = message || RESPONSE_MESSAGES.SUCCESS;
    data = data || [];
    return res
        .status(Code)
        .send({ code: Code, message: message, data: data });
};

exports.sendErrorResponse = (req, res, Code, error) => {
    Code ||= StatusCodes.BAD_REQUEST;
    error ||= RESPONSE_MESSAGES.BAD_REQUEST;
    return res.status(Code).send({
        code: Code,
        message: error,
    });
};

exports.unAuthorizedResponse = (req, res, message) => {
    const Code = StatusCodes.UNAUTHORIZED_ACCESS;
    message = message || RESPONSE_MESSAGES.UNAUTHORIZED_ACCESS;
    return res.status(Code).send({
        code: Code,
        message: message,
        data: {},
    });
};

exports.AccessForbiddenResponse = (req, res, message) => {
    const Code = StatusCodes.ACCESS_FORBIDDEN;
    message = message || RESPONSE_MESSAGES.ACCESS_FORBIDDEN;
    return res.status(Code).send({
        code: Code,
        message: message,
        data: {},
    });
};
exports.sendIntnernalServerError = (res, err) => {
    const Code = StatusCodes.INTERNAL_SERVER_ERROR;
    return res.status(Code).json({
        code: Code,
        message: RESPONSE_MESSAGES.INTERNAL_SERVER_ERROR,
        error: err?.stack || err,
    });
};

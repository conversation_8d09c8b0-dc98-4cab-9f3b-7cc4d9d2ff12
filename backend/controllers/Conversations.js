const { RESPONSE_MESSAGES } = require('../messages/responses');
const ConversationModel = require('../models/Conversations');
const Models = require("../models")
const { StatusCodes } = require('../statusCodes');
const { sendErrorResponse, sendResponse, sendIntnernalServerError } = require("../utils/responses")

exports.createConversation = async (req, res) => {
    try {
        const { title, isDeepResearch } = req.body;
        if (!title) {
            return sendErrorResponse(req, res, StatusCodes.BAD_REQUEST, "Title is required")
        }
        const newConversation = new ConversationModel({
            userId: req.user.id,
            title,
            isDeepResearch
        });
        await newConversation.save();
        sendResponse(req, res, StatusCodes.CREATED, RESPONSE_MESSAGES.CONVERSATION_CREATED, newConversation)
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
};

exports.getConversations = async (req, res) => {
    try {
        const { isDeepResearch } = req.query;
        const criteria = [
            {
                $match: {
                    userId: req.user.id,
                }
            }
        ]
        if (isDeepResearch == "false") {
            criteria[0].$match.isDeepResearch = false;
            criteria.push(
                {
                    $lookup: {
                        from: "messages",
                        localField: "_id",
                        foreignField: "conversationId",
                        as: "messages"
                    }
                },
                {
                    $match: {
                        $expr: {
                            $gt: [{ $size: "$messages" }, 1]
                        }
                    }
                },
                {
                    $project: {
                        messages: 0
                    }
                })
        }else if(isDeepResearch == "true") {
            criteria[0].$match.isDeepResearch = true;        
        }
        criteria.push({
            $sort: { createdAt: -1 }
        });

        const conversations = await ConversationModel.aggregate(criteria)
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.CONVERSATIONS_FOUND, conversations)
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
}

exports.getConversationsById = async (req, res) => {
    try {
        const { conversationId } = req.params;
        const conversation = await ConversationModel.findOne({ _id: conversationId })
        if (!conversation) return sendErrorResponse(req, res, StatusCodes.NOT_FOUND, RESPONSE_MESSAGES.CONVERSATION_NOT_FOUND)
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.CONVERSATION_FOUND, conversation)
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
}

exports.updateConversationById = async (req, res) => {
    try {
        const { conversationId } = req.params;
        const udpatedConversation = await ConversationModel.findOneAndUpdate({ _id: conversationId }, req.body, { new: true })
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.CONVERSATION_UPDATED, udpatedConversation)
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
}

exports.deleteConversationById = async (req, res) => {
    try {
        const { conversationId } = req.params;
        await Models.Conversations.deleteOne({ _id: conversationId })
        const chatsForConversationId = await Models.Chats.find({ conversationId: conversationId }).distinct("_id")
        const messagesForChats = await Models.Messages.find({ chatId: { $in: chatsForConversationId } }).distinct("_id")
        await Models.References.deleteMany({ messageId: { $in: messagesForChats } })
        await Models.Messages.deleteMany({ chatId: { $in: chatsForConversationId } })
        await Models.Chats.deleteMany({ conversationId: conversationId })
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.CONVERSATION_DELETED, {})
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
}
const { default: axios } = require('axios');
const { RESPONSE_MESSAGES } = require('../messages/responses');
const priceModel = require('../models/Price');
const { StatusCodes } = require('../statusCodes');
const { sendErrorResponse, sendResponse, sendIntnernalServerError } = require('../utils/responses');
const Models = require('../models');
const stripe = require('../config/stripe');

exports.updatePayments = async (req, res) => {
    let event;
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET
    try {
        const sig = req.headers['stripe-signature'];
        console.log({sig,  endpointSecret }, "lkjlkjlkjlkjlkjlkj")
        event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
    } catch (err) {
        console.error('❌ Webhook signature verification failed:', err.message);
        return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    try {
        if (event.type !== "checkout.session.completed") return res.status(200).end();

        const session = event.data.object

        const subscription = await stripe.subscriptions.retrieve(session.subscription);
        const user = await Models.Users.findOne({ stripeCustomerId: session.customer }).populate("subscriptions");

        const activeSubscriptions = user.subscriptions.filter(item => item.status === "active");
        await Promise.allSettled(activeSubscriptions.map(async item => {
            try {
                await stripe.subscriptions.update(item.subscriptionId, {
                    cancel_at_period_end: false
                });
                await stripe.subscriptions.cancel(item.subscriptionId);
                await Models.Subscriptions.findOneAndUpdate(
                    { subscriptionId: item.subscriptionId },
                    { status: "canceled", cancelAtPeriodEnd: false }
                );
            } catch (err) {
                console.error('Error during cancelling subscriptions:', err);
            }
        }));

        const subscriptionData = {
            userId: user._id,
            subscriptionId: subscription.id,
            customerId: session.customer,
            checkoutSessionId: session.id,
            invoiceId: session.invoice,
            priceId: subscription.items.data[0].price.id,
            productId: subscription.items.data[0].price.product,
            amount: session.amount_total,
            currency: session.currency,
            status: subscription.status,
            paymentStatus: session.payment_status,
            cancelAtPeriodEnd: subscription.cancel_at_period_end,
            currentPeriodStart: subscription.current_period_start ? new Date(subscription.current_period_start * 1000) : null,
            currentPeriodEnd: subscription.current_period_end ? new Date(subscription.current_period_end * 1000) : null,
        };

        const newSubscription = new Models.Subscriptions(subscriptionData);
        await newSubscription.save();

        user.subscriptions.push(newSubscription._id);
        await user.save();

        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.SUCCESS, { message: "Webhook executed successfully" });
    } catch (error) {
        console.error('Internal Error:', error);
        sendIntnernalServerError(res, error.message);
    }
};
const { default: axios } = require('axios');
const { RESPONSE_MESSAGES } = require('../messages/responses');
const productsModel = require('../models/Products');
const { StatusCodes } = require('../statusCodes');
const { sendErrorResponse, sendResponse, sendIntnernalServerError } = require('../utils/responses');
const stripe = require("../config/stripe");

exports.getAllProducts = async (req, res) => {
    try {
        const products = await stripe.products.list({ active: true });

        const prices = await stripe.prices.list({ active: true });

        const pricesByProduct = {};
        prices.data.forEach(price => {
            if (!pricesByProduct[price.product]) {
                pricesByProduct[price.product] = [];
            }
            pricesByProduct[price.product].push(price);
        });

        const productsWithPrices = products.data.map(product => {
            const productPrices = pricesByProduct[product.id] || [];

            return {
                id: product.id,
                name: product.name,
                description: product.description,
                images: product.images,
                prices: productPrices,
                metadata: product.metadata
            };
        });

        const sortedProducts = productsWithPrices.sort((a, b) => {
            const getMonthlyPrice = (product) => {
                const monthly = product.prices.find(p => p.recurring?.interval === "month");
                return monthly?.unit_amount || 0;
            };

            return getMonthlyPrice(a) - getMonthlyPrice(b);
        });

        res.json(sortedProducts);
    } catch (error) {
        console.error('Error fetching products from Stripe:', error);
        res.status(500).json({ error: 'Failed to fetch products' });
    }
}
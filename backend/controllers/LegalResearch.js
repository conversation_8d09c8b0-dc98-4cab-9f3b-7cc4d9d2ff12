const { default: axios } = require('axios');
const { RESPONSE_MESSAGES } = require('../messages/responses');
const Models = require('../models');
const { StatusCodes } = require('../statusCodes');
const { sendErrorResponse, sendResponse, sendIntnernalServerError } = require('../utils/responses');
const { uploadToS3 } = require('../utils/s3');
const mockedResponses = require("./mockedRFPResponses")

exports.createLegalResearch = async (req, res) => {
    try {
        const { conversationId, reportType, mode = "" } = req.body;
        let user = await Models.Users.findOne({ _id: req.user.id })
        const images = req.files.filter(item => item.fieldname == 'image[]') || [];
        const files = req.files.filter(item => item.fieldname == 'file[]') || []
        const audios = req.files.filter(item => item.fieldname == 'audio[]') || []

        const uploadCategory = async (fileArray, type) => {
            return await Promise.all(
                fileArray.map(async (file) => {
                    const key = `${req.user.id}/${conversationId}/${file.originalname}`.replace(/\s+/g, '_');
                    try {


                        await uploadToS3(file.buffer, key, file.mimetype);
                        const fileUrl = `https://${process.env.AWS_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;
                        return {
                            // name: file.originalname,
                            // url: fileUrl,
                            // type: file.mimetype,
                            key: key,
                            bucket: process.env.AWS_BUCKET_NAME,
                            region: process.env.AWS_REGION,
                        };
                    } catch (error) {
                        console.error(`Error uploading ${type} file:`, error);
                        return null;
                    }
                })
            );
        };

        const uploadedImages = await uploadCategory(images, 'image');
        const uploadedFiles = await uploadCategory(files, 'file');
        const uploadedAudios = await uploadCategory(audios, 'audio');
        const rfpFiles = [...uploadedImages, ...uploadedFiles, ...uploadedAudios].filter(Boolean)


        const aiPayload = {
            user,
            rfpfiles: {
                files: rfpFiles,
            }
        };
        let reportCreated;

        try {
            const response = await axios.post(
                `${process.env.AI_SERVER}/api/v1/rfpagent`,
                aiPayload
            );
            if (response.data) {
                reportCreated = await Models.Reports.create({ ...response.data, conversationId: conversationId, type: "rfp" })
            }
            response.data.reportId = reportCreated?._id
            sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.SUCCESS, response.data);
        } catch (err) {
            if (reportType === "Working Legal Memo") {
                reportCreated = await Models.Reports.create({ ...mockedResponses, conversationId: conversationId, type: "rfp" })
                mockedResponses.reportId = reportCreated._id
                sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.SUCCESS, mockedResponses)
            }
             if (reportType === "Plain-language Summary") {
                reportCreated = await Models.Reports.create({ ...mockedResponses, conversationId: conversationId, type: "rfp" })
                mockedResponses.reportId = reportCreated._id
                sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.SUCCESS, mockedResponses)
            }
             if (reportType === "Legal Opinion Letter") {
                reportCreated = await Models.Reports.create({ ...mockedResponses, conversationId: conversationId, type: "rfp" })
                mockedResponses.reportId = reportCreated._id
                sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.SUCCESS, mockedResponses)
            }
            console.log("Error in AI payload creation", err);
        }

    } catch (err) {
        console.log(err, "error creating rfp", err)
        sendErrorResponse(req, res, StatusCodes.INTERNAL_SERVER_ERROR, RESPONSE_MESSAGES.INTERNAL_SERVER_ERROR)
    }
}

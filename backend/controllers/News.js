const { default: axios } = require('axios');
const { RESPONSE_MESSAGES } = require('../messages/responses');
const NewsModel = require('../models/News');
const { StatusCodes } = require('../statusCodes');
const { sendErrorResponse, sendResponse, sendIntnernalServerError } = require('../utils/responses');

exports.getAllNews = async (req, res) => {
    try {
        const News = await NewsModel.find().lean()
        const types = News[0] || {}
        const { country, category, language, search } = types
        let newsCriteria = `${country ? "&country=" + country : ""}${category ? "&category=" + category : ""}${language ? "&language=" + language : ""}`
        try{
            let latestNews = await axios.get("https://newsdata.io/api/1/latest?apikey=" + process.env.NEWS_API_KEY + newsCriteria)
            sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.NEWS_FETCHED, latestNews?.data || [])
        }catch(err){
            console.log(err.status,'lkjlkjlkjlkjlkj')
            sendErrorResponse(req, res, err.status, err?.response?.data?.results?.message)
        }
    } catch (err) {
        sendIntnernalServerError(res, err.message)
    }
};

exports.updateNewsTypes = async (req, res, next) => {
    try {
        let udpatedNews = await NewsModel.updateMany({}, req.body, { upsert: true })
        udpatedNews = await NewsModel.find()
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.NEWS_UPDATED, udpatedNews)
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
}

exports.getNewsType = async (req, res, next) => {
    try {
        let news = await NewsModel.findOne({})
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.NEWS_FETCHED, news)
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
}
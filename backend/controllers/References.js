const ReferenceModel = require('../models/References');

exports.createReference = async (req, res) => {
    const { messageId, type, content, metadata, sourceUrl } = req.body;

    if (!messageId) {
        return res.status(400).json({ code: 400, message: 'Invalid request' });
    }

    const newReference = new ReferenceModel({
        messageId,
        type,
        content,
        metadata,
        sourceUrl,
        createdAt: new Date(),
    });

    await newReference.save();
    res.status(201).json(newReference);
};

exports.getReferenceById = async (req, res) => {
    const { referenceId } = req.params;

    if (!referenceId) {
        return res.status(400).json({ code: 400, message: 'Invalid request' });
    }

    const reference = {
        id: referenceId,
        messageId: '123e4567-e89b-12d3-a456-426614174002',
        type: 'document',
        content: 'Reference content',
        metadata: {},
        sourceUrl: 'https://example.com/article/12345',
        createdAt: new Date().toISOString(),
    };

    res.status(200).json(reference);
}
exports.updateReference = async (req, res) => {
    const { referenceId } = req.params;
    const { content, sourceUrl } = req.body;

    if (!referenceId || (!content && !sourceUrl)) {
        return res.status(400).json({ code: 400, message: 'Invalid request' });
    }

    const updatedReference = await ReferenceModel.findByIdAndUpdate(referenceId, {
        content,
        sourceUrl,
        createdAt: new Date(),
    }, { new: true });

    if (!updatedReference) {
        return res.status(404).json({ code: 404, message: 'Reference not found' });
    }

    res.status(200).json(updatedReference);
};
exports.deleteReferenceById = async (req, res) => {
    const { referenceId } = req.params;
    if (!referenceId) {
        return res.status(400).json({ code: 400, message: 'Invalid request' });
    }
    res.status(204).send();
}
exports.getReferenceByMessageId = async (req, res) => {
    const { messageId } = req.params;
    if (!messageId) {
        return res.status(400).json({ code: 400, message: 'Invalid request' });
    }
    const references = await ReferenceModel.find({ messageId });
    res.status(200).json(references);
}
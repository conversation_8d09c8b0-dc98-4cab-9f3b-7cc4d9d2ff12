const { RESPONSE_MESSAGES } = require('../messages/responses');
const GoalModel = require('../models/Goals');
const UserModel = require('../models/Users');
const { StatusCodes } = require('../statusCodes');
const { sendIntnernalServerError, sendResponse, sendErrorResponse } = require('../utils/responses');

// Get all goals for the logged-in user
exports.getAllGoals = async (req, res) => {
    try {
        const goals = await GoalModel.find({ user: req.user.id });
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.GOAL_FETCHED, goals);
    } catch (err) {
        sendIntnernalServerError(res, err);
    }
}

// Get a single goal by ID
exports.getGoalById = async (req, res) => {
    try {
        const goal = await GoalModel.findOne({ _id: req.params.goalId, user: req.user.id });
        if (!goal) return sendErrorResponse(req, res, StatusCodes.NOT_FOUND, RESPONSE_MESSAGES.GOAL_NOT_FOUND);
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.GOAL_FETCHED, goal);
    } catch (err) {
        sendIntnernalServerError(res, err);
    }
}

// Update a goal by ID
exports.updateGoalById = async (req, res) => {
    try {
        const goal = await GoalModel.findOneAndUpdate(
            { _id: req.params.goalId, user: req.user.id },
            req.body,
            { new: true }
        );
        if (!goal) return sendErrorResponse(req, res, StatusCodes.NOT_FOUND, RESPONSE_MESSAGES.GOAL_NOT_FOUND);
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.GOAL_UPDATED, goal);
    } catch (err) {
        sendIntnernalServerError(res, err);
    }
}

// Create a new goal
exports.createGoal = async (req, res) => {
    try {
        const { title, description } = req.body;
        if (!title) {
            return res.status(400).json({ code: 400, message: 'Title is required' });
        }
        const newGoal = new GoalModel({
            title,
            description,
            user: req.user.id,
        });
        await newGoal.save();
        UserModel.findOneAndUpdate(
            { _id: req.user.id },
            { $push: { goals: newGoal._id } }
        )
        sendResponse(req, res, StatusCodes.CREATED, RESPONSE_MESSAGES.GOAL_CREATED, newGoal);
    } catch (err) {
        sendIntnernalServerError(res, err);
    }
}

// Delete a goal by ID
exports.deleteGoal = async (req, res) => {
    try {
        const goalFound = await GoalModel.findOneAndDelete({ _id: req.params.goalId, user: req.user.id });
        if (!goalFound) return sendErrorResponse(req, res, StatusCodes.NOT_FOUND, RESPONSE_MESSAGES.GOAL_NOT_FOUND);
        UserModel.findOneAndUpdate(
            { _id: req.user.id },
            { $pull: { goals: req.params.goalId } }
        )
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.GOAL_DELETED);
    } catch (err) {
        sendIntnernalServerError(res, err);
    }
}
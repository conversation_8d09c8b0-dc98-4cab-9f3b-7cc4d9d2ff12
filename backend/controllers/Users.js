const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const { v4: uuidv4 } = require("uuid");
const mongoose = require("mongoose");
const UserModel = require("../models/Users.js");
const Models = require("../models");
const ImageModel = require("../models/Image.js");
const UserToken = require("../models/UserToken");
const { StatusCodes } = require("../statusCodes");
const { RESPONSE_MESSAGES } = require("../messages/responses");
const sendMail = require("../services/sendMail");
const {
  sendIntnernalServerError,
  sendResponse,
  sendErrorResponse,
} = require("../utils/responses.js");
const stripe = require("../config/stripe.js");
const Constants = require("../constants");

// exports.registerUser = async (req, res) => {
//     try {
//         console.log(req.query," query we are getting")
//         const { username, password, email, firstName, lastName } = req.body;
//         const hashedPassword = await bcrypt.hash(password, 10);

//         const foundUser = await UserModel.findOne({ $or: [{ username }, { email }] }).lean().exec();
//         let errorMessage = ""
//         if (foundUser?.username == username) errorMessage = "Username already exists"
//         if (foundUser?.email == email) errorMessage = "email already exists"
//         if (foundUser) return res.status(400).json({ code: 400, message: errorMessage });
//         const newUser = new UserModel({ username, password: hashedPassword, email, firstName, lastName });
//         await ImageModel.create({ uploadedBy: newUser._id, url: process.env.DEFAULT_GALLERY_PIC })
//         await newUser.save();
//         const jti = uuidv4();
//         const token = jwt.sign({ id: newUser._id, jti }, process.env.JWT_SECRET, { expiresIn: "1h" });
//         await UserToken.create({ userId: newUser._id, jti, });
//         res.status(201).json({ message: 'User registered', code: 201, data: { user: newUser._doc, token } });
//     } catch (err) {
//         res.status(500).json({ code: 0, message: "Internal Server Error", details: err.message });
//     }
// };

exports.registerUser = async (req, res) => {
  try {
    const { username, password, email, firstName, lastName } = req.body;
    const hashedPassword = await bcrypt.hash(password, 10);

    const foundUser = await UserModel.findOne({ $or: [{ username }, { email }] }).lean().exec();
    let errorMessage = "";
    if (foundUser?.username === username) errorMessage = "Username already exists";
    if (foundUser?.email === email) errorMessage = "Email already exists";
    if (foundUser) return res.status(400).json({ code: 400, message: errorMessage });

    const newUser = new UserModel({
      username,
      password: hashedPassword,
      email,
      firstName,
      lastName,
      isEmailVerified: false,
    });

    await ImageModel.create({
      uploadedBy: newUser._id,
      url: process.env.DEFAULT_GALLERY_PIC,
    });

    await newUser.save();

    const jti = uuidv4();
    const token = jwt.sign({ id: newUser._id, email: newUser.email, jti }, process.env.JWT_SECRET, { expiresIn: "1h" });
    await UserToken.create({ userId: newUser._id, jti });

    const today = new Date();
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    const formattedDate = today.toLocaleDateString('en-US', options);
    await sendMail(newUser.email, {
      // html: `<a href="${process.env.MERGEN_WEB_APP_BACKEND}/api/v1/users/verify?token=${token}&type=register" 
      //           style="display:inline-block; padding:10px 20px; background:#007bff; color:#fff; text-decoration:none; border-radius:5px;">
      //           Verify Your Email
      //        </a>`,
      html: `<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Welcome to Mergen AI</title>
  </head>
  <body style="margin:0; padding:0; background-color:#f3f4f6; font-family: Arial, sans-serif;">
    <table align="center" width="100%" cellpadding="0" cellspacing="0" style="background-color:#f3f4f6; padding: 40px 0;">
      <tr>
        <td align="center">
          <table width="600" cellpadding="0" cellspacing="0" style="background-color:#ffffff; border-radius:12px; overflow:hidden; box-shadow:0 4px 12px rgba(0,0,0,0.1);">
            <!-- Header -->
            <tr>
              <td style="padding:24px; border-bottom:1px solid #e5e7eb;">
                <table width="100%" cellpadding="0" cellspacing="0">
                  <tr>
                    <td align="left">
                      <img src="https://mergen-ai-s3.s3.ca-central-1.amazonaws.com/icon+(1).png" alt="Mergen AI Icon" width="32" height="32" style="vertical-align:middle; margin-right:8px;">
                      <img src="https://mergen-ai-s3.s3.ca-central-1.amazonaws.com/MergenAILogo.png" alt="Mergen AI Logo" height="32" style="vertical-align:middle;">
                    </td>
                    <td align="right" style="font-size:14px; color:#6b7280;">
                      ${formattedDate}
                    </td>
                  </tr>
                </table>
              </td>
            </tr>

            <!-- Main Content -->
            <tr>
              <td style="padding:32px 24px;">
                <table width="100%" cellpadding="0" cellspacing="0">
                  <tr>
                    <td align="center" style="padding-bottom: 16px;">
  <table width="64" height="64" cellpadding="0" cellspacing="0" border="0" style="background-color: #ecfdf5; border-radius: 50%;">
    <tr>
      <td align="center" valign="middle">
        <img src="https://mergen-ai-s3.s3.ca-central-1.amazonaws.com/check-circle.png" width="32" height="32" alt="Checkmark" style="display:block;" />
      </td>
    </tr>
  </table>
</td>
                  </tr>
                  <tr>
                    <td align="center" style="font-size:24px; font-weight:bold; color:#111827; padding-top: 16px; padding-bottom:8px;">
                      Welcome to Mergen AI!
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="color:#4b5563; font-size:16px; line-height:1.6;">
                      Thank you for signing up.
                    </td>
                  </tr>
                  <tr>
                    <td style="padding-top: 24px; font-size:16px; color:#4b5563;">
                      Hello <strong>${firstName+" "+lastName}</strong>,
                      <br><br>
                      We're excited to have you join our community. Mergen AI is a secure, multi-agent productivity engine built for the modern knowledge economy. Your account has been created successfully, but before you can start exploring all that Mergen AI has to offer, please verify your email address.
                    </td>
                  </tr>

                  <!-- CTA Button -->
                  <tr>
                    <td align="center" style="padding-top: 24px;">
                      <a href="${process.env.MERGEN_WEB_APP_BACKEND}/api/v1/users/verify?token=${token}&type=register"  style="display:inline-block; background: linear-gradient(to right, #0A2540, #134074); color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: bold; font-size:16px;">
                        Verify Email →
                      </a>
                    </td>
                  </tr>

                  <tr>
                    <td style="padding-top:24px; font-size:16px; color:#4b5563;">
                      Once verified, you'll gain access to our platform where you can:
                    </td>
                  </tr>

                  <tr>
                    <td style="padding-top:16px;">
                      <ul style="padding-left: 20px; font-size:16px; color:#4b5563; line-height:1.6;">
                        <li><strong>🔍 Unify your AI Agents</strong> – One secure platform for research, writing, data analysis, and more</li>
                        <li><strong>⚙️ Automate repetitive tasks</strong> – Let AI agents handle the busywork</li>
                        <li><strong>📊 Turn data into insight</strong> – Extract key points and generate reports quickly</li>
                        <li><strong>🧠 Built for professionals</strong> – Designed for client-facing work and decision-making</li>
                        <li><strong>🌐 Perfect for everyday use</strong> – From business memos to travel planning</li>
                      </ul>
                    </td>
                  </tr>

                  <tr>
                    <td style="font-size:16px; color:#4b5563;">
                      If you have any questions or need assistance, our support team is always ready to help.
                      <br><br>
                      Best regards,  
                      <br>The Mergen AI Team
                    </td>
                  </tr>
                </table>
              </td>
            </tr>

            <!-- Footer -->
            <tr>
              <td style="background-color:#f9fafb; padding:24px; font-size:14px; color:#6b7280; border-top:1px solid #e5e7eb;">
                <table width="100%" cellpadding="0" cellspacing="0">
                  <tr>
                    <td align="center" style="padding-bottom: 8px;">
                      <a href="https://www.linkedin.com/company/mergen-aiai/" target="_blank" style="color: #6b7280; text-decoration: none;">LinkedIn</a>
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="font-size:14px; padding-bottom:8px;">
                      This email was sent to you because you signed up for Mergen AI. If you didn’t create this account, please ignore this email or <a href="mailto:<EMAIL>" style="color:#00C9A7; text-decoration: none;">contact support</a>.
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="border-top:1px solid #e5e7eb; padding-top:12px;">
                      © 2025 Mergen AI. All rights reserved.
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="font-size:12px; color:#9ca3af;">
                      Mergen AI, 35 McCaul St, Toronto, ON M5T 1V7
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>
`,
      subject: "Welcome to Mergen AI"
    });

    res.status(201).json({
      message: "Your account has been successfully created. Please check your email to verify your account.",
      code: 201,
      data: { user: newUser._doc, token },
    });
  } catch (err) {
    res.status(500).json({
      code: 0,
      message: "Internal Server Error",
      details: err.message,
    });
  }
};

exports.userVerification = async (req, res) => {
  try {
    const { token, type } = req.query;
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    const user = await UserModel.findOne({ email: decoded.email });
    if (!user) return res.status(400).json({ message: "Invalid user" });
    const info = await UserModel.findOneAndUpdate({ email: decoded.email }, { $set: { isEmailVerified: true } }, { new: true })

    const subscriptions = await Models.Subscriptions.find({ userId: user._id, status: "active" })
    if (!(user.stripeCustomerId || subscriptions.length)) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: `${user.firstName} ${user.lastName}`,
        metadata: { mongoUserId: user._id.toString() },
      });

      const customerId = customer.id;
      user.stripeCustomerId = customerId;
      await user.save();

      const subscription = await stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: process.env.FREE_PRODUCT_PRICE_ID }],
        // trial_period_days: 0,
      });
      if (subscription.latest_invoice) {
        const invoice = await stripe.invoices.retrieve(
          subscription.latest_invoice
        );
        const subscriptionData = {
          userId: user._id,
          subscriptionId: subscription.id,
          customerId: subscription.customer,
          checkoutSessionId: null,
          invoiceId: invoice?.id || null,
          priceId: subscription.items.data[0].price.id,
          productId: subscription.items.data[0].price.product,
          amount: invoice?.amount_paid || 0,
          currency: invoice?.currency || "cad",
          status: subscription.status,
          paymentStatus: invoice?.status || "no_payment_required",
          cancelAtPeriodEnd: subscription.cancel_at_period_end,
          currentPeriodStart: subscription.current_period_start
            ? new Date(subscription.current_period_start * 1000)
            : null,
          currentPeriodEnd: subscription.current_period_end
            ? new Date(subscription.current_period_end * 1000)
            : null,
        };
        const newSubscription = new Models.Subscriptions(subscriptionData);
        await newSubscription.save();
        user.subscriptions.push(newSubscription._id);
        await user.save();
      }
    }
    const page = type === "login" ? `chat?showSubscription=${false}` : `chat?showSubscription=${true}`
    if (info.isEmailVerified) {
      return res.redirect(`${process.env.DEEP_MERGEN_FRONTEND}/${page}&token=${token}`);
    }
  } catch (err) {
    console.log(err, "err we got")
    res.status(400).json({ message: "Invalid or expired token" });
  }
};

exports.loginUser = async (req, res) => {
  try {
    const { username, password } = req.body;
    const user = await UserModel.findOne({ username }).populate("goals");
    const jti = uuidv4();

    if (!user || !(await bcrypt.compare(password, user.password))) {
      return res
        .status(400)
        .json({ code: 400, message: "Invalid username or password" });
    }

    const token = jwt.sign({ id: user._id, email: user.email, jti }, process.env.JWT_SECRET, {
      expiresIn: "1h",
    });
    await UserToken.create({ userId: user._id, jti });

    const today = new Date();
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    const formattedDate = today.toLocaleDateString('en-US', options);

    if (!user.isEmailVerified) {
      await sendMail(user.email, {
        // html: `<a href="${process.env.MERGEN_WEB_APP_BACKEND}/api/v1/users/verify?token=${token}&type=login" 
        //                 style="display:inline-block; padding:10px 20px; background:#007bff; color:#fff; text-decoration:none; border-radius:5px;">
        //                 Verify Your Email
        //             </a>`,

        html: `<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Welcome to Mergen AI</title>
  </head>
  <body style="margin:0; padding:0; background-color:#f3f4f6; font-family: Arial, sans-serif;">
    <table align="center" width="100%" cellpadding="0" cellspacing="0" style="background-color:#f3f4f6; padding: 40px 0;">
      <tr>
        <td align="center">
          <table width="600" cellpadding="0" cellspacing="0" style="background-color:#ffffff; border-radius:12px; overflow:hidden; box-shadow:0 4px 12px rgba(0,0,0,0.1);">
            <!-- Header -->
            <tr>
              <td style="padding:24px; border-bottom:1px solid #e5e7eb;">
                <table width="100%" cellpadding="0" cellspacing="0">
                  <tr>
                    <td align="left">
                      <img src="https://mergen-ai-s3.s3.ca-central-1.amazonaws.com/icon+(1).png" alt="Mergen AI Icon" width="32" height="32" style="vertical-align:middle; margin-right:8px;">
                      <img src="https://mergen-ai-s3.s3.ca-central-1.amazonaws.com/MergenAILogo.png" alt="Mergen AI Logo" height="32" style="vertical-align:middle;">
                    </td>
                    <td align="right" style="font-size:14px; color:#6b7280;">
                      ${formattedDate}
                    </td>
                  </tr>
                </table>
              </td>
            </tr>

            <!-- Main Content -->
            <tr>
              <td style="padding:32px 24px;">
                <table width="100%" cellpadding="0" cellspacing="0">
                  <tr>
                    <td align="center" style="padding-bottom: 16px;">
  <table width="64" height="64" cellpadding="0" cellspacing="0" border="0" style="background-color: #ecfdf5; border-radius: 50%;">
    <tr>
      <td align="center" valign="middle">
        <img src="https://mergen-ai-s3.s3.ca-central-1.amazonaws.com/check-circle.png" width="32" height="32" alt="Checkmark" style="display:block;" />
      </td>
    </tr>
  </table>
</td>
                  </tr>
                  <tr>
                    <td align="center" style="font-size:24px; font-weight:bold; color:#111827; padding-top: 16px; padding-bottom:8px;">
                      Welcome to Mergen AI!
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="color:#4b5563; font-size:16px; line-height:1.6;">
                      Thank you for signing up.
                    </td>
                  </tr>
                  <tr>
                    <td style="padding-top: 24px; font-size:16px; color:#4b5563;">
                      Hello <strong>${user.firstName +" "+ user.lastName}</strong>,
                      <br><br>
                      We're excited to have you join our community. Mergen AI is a secure, multi-agent productivity engine built for the modern knowledge economy. Your account has been created successfully, but before you can start exploring all that Mergen AI has to offer, please verify your email address.
                    </td>
                  </tr>

                  <!-- CTA Button -->
                  <tr>
                    <td align="center" style="padding-top: 24px;">
                      <a href="${process.env.MERGEN_WEB_APP_BACKEND}/api/v1/users/verify?token=${token}&type=login"  style="display:inline-block; background: linear-gradient(to right, #0A2540, #134074); color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: bold; font-size:16px;">
                        Verify Email →
                      </a>
                    </td>
                  </tr>

                  <tr>
                    <td style="padding-top:24px; font-size:16px; color:#4b5563;">
                      Once verified, you'll gain access to our platform where you can:
                    </td>
                  </tr>

                  <tr>
                    <td style="padding-top:16px;">
                      <ul style="padding-left: 20px; font-size:16px; color:#4b5563; line-height:1.6;">
                        <li><strong>🔍 Unify your AI Agents</strong> – One secure platform for research, writing, data analysis, and more</li>
                        <li><strong>⚙️ Automate repetitive tasks</strong> – Let AI agents handle the busywork</li>
                        <li><strong>📊 Turn data into insight</strong> – Extract key points and generate reports quickly</li>
                        <li><strong>🧠 Built for professionals</strong> – Designed for client-facing work and decision-making</li>
                        <li><strong>🌐 Perfect for everyday use</strong> – From business memos to travel planning</li>
                      </ul>
                    </td>
                  </tr>

                  <tr>
                    <td style="font-size:16px; color:#4b5563;">
                      If you have any questions or need assistance, our support team is always ready to help.
                      <br><br>
                      Best regards,  
                      <br>The Mergen AI Team
                    </td>
                  </tr>
                </table>
              </td>
            </tr>

            <!-- Footer -->
            <tr>
              <td style="background-color:#f9fafb; padding:24px; font-size:14px; color:#6b7280; border-top:1px solid #e5e7eb;">
                <table width="100%" cellpadding="0" cellspacing="0">
                  <tr>
                    <td align="center" style="padding-bottom: 8px;">
                      <a href="https://www.linkedin.com/company/mergen-aiai/" target="_blank" style="color: #6b7280; text-decoration: none;">LinkedIn</a>
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="font-size:14px; padding-bottom:8px;">
                      This email was sent to you because you signed up for Mergen AI. If you didn’t create this account, please ignore this email or <a href="mailto:<EMAIL>" style="color:#00C9A7; text-decoration: none;">contact support</a>.
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="border-top:1px solid #e5e7eb; padding-top:12px;">
                      © 2025 Mergen AI. All rights reserved.
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="font-size:12px; color:#9ca3af;">
                      Mergen AI, 35 McCaul St, Toronto, ON M5T 1V7
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>
`,
        subject: "Welcome to Mergen AI"
      });
      return res
        .status(200)
        .json({ code: 200, message: "Your email is not verified yet. Please check your inbox and confirm your email" });
    }

    const subscriptions = await Models.Subscriptions.find({ userId: user._id });
    const activeSubscriptions = subscriptions.filter(
      (item) => item.status === "active"
    );

    if (!activeSubscriptions.length) {
      let stripeCustomerId = user.stripeCustomerId;
      if (!stripeCustomerId) {
        const customer = await stripe.customers.create({
          email: user.email,
          name: `${user.firstName} ${user.lastName}`,
          metadata: { mongoUserId: user._id.toString() },
        });

        stripeCustomerId = customer.id;
        user.stripeCustomerId = stripeCustomerId;
        await user.save();
      }

      const subscription = await stripe.subscriptions.create({
        customer: stripeCustomerId,
        items: [{ price: process.env.FREE_PRODUCT_PRICE_ID }],
        // trial_period_days: 0,
      });


      if (subscription.latest_invoice) {
        const invoice = await stripe.invoices.retrieve(
          subscription.latest_invoice
        );
        const subscriptionData = {
          userId: user._id,
          subscriptionId: subscription.id,
          customerId: subscription.customer,
          checkoutSessionId: null,
          invoiceId: invoice?.id || null,
          priceId: subscription.items.data[0].price.id,
          productId: subscription.items.data[0].price.product,
          amount: invoice?.amount_paid || 0,
          currency: invoice?.currency || "cad",
          status: subscription.status,
          paymentStatus: invoice?.status || "no_payment_required",
          cancelAtPeriodEnd: subscription.cancel_at_period_end,
          currentPeriodStart: subscription.current_period_start
            ? new Date(subscription.current_period_start * 1000)
            : null,
          currentPeriodEnd: subscription.current_period_end
            ? new Date(subscription.current_period_end * 1000)
            : null,
        };
        const newSubscription = new Models.Subscriptions(subscriptionData);
        await newSubscription.save();
        user.subscriptions.push(newSubscription._id);
        await user.save();
      }
    }
    const result = await Promise.all(
      activeSubscriptions.map(async (item) => {
        const result = await stripe.products.retrieve(item.productId);
        return { ...item._doc, product: result };
      })
    );

    res.json({ token, user: { ...user._doc, subscriptions: result } });
  } catch (err) {
    console.log(err, "erororororro");
    res
      .status(500)
      .json({
        code: 500,
        message: "Internal Server Error",
        details: err.message,
      });
  }
};

exports.changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword, resetToken } = req.body;
    const user = await UserModel.findOne({ resetToken }).populate("goals");
    if (!user)
      return sendErrorResponse(
        req,
        res,
        StatusCodes.BAD_REQUEST,
        RESPONSE_MESSAGES.INVALID_TOKEN
      );
    if (user.expireTokenIn < Date.now())
      return sendErrorResponse(
        req,
        res,
        StatusCodes.BAD_REQUEST,
        RESPONSE_MESSAGES.OTP_EXPIRED
      );
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);
    user.password = hashedNewPassword;
    await user.save();
    const jti = uuidv4();
    const token = jwt.sign({ id: user._id, jti }, process.env.JWT_SECRET, {
      expiresIn: "1h",
    });
    await UserToken.create({ userId: user._id, jti });
    res
      .status(200)
      .json({
        message: "Password changed successfully",
        code: 200,
        data: { user, token },
      });
  } catch (err) {
    res
      .status(500)
      .json({
        code: 0,
        message: "Internal Server Error",
        details: err.message,
      });
  }
};

exports.updatePassword = async (req, res) => {
  try {
    const { newPassword, oldPassword = "" } = req.body;
    const user = await UserModel.findOne({ _id: req.user?.id }).populate(
      "goals"
    );
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);
    if (!user || !(await bcrypt.compare(oldPassword, user.password))) {
      return res
        .status(400)
        .json({ code: 400, message: "Invalid Old Password" });
    }
    user.password = hashedNewPassword;
    await user.save();
    const jti = uuidv4();
    const token = jwt.sign({ id: user._id, jti }, process.env.JWT_SECRET, {
      expiresIn: "1h",
    });
    await UserToken.deleteMany({ userId: req.user.id });
    await UserToken.create({ userId: user._id, jti });
    sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.PASSWORD_UDPATED, {
      user,
      token,
    });
  } catch (err) {
    res
      .status(500)
      .json({
        code: 0,
        message: "Internal Server Error",
        details: err.message,
      });
  }
};

exports.forgetPassword = async (req, res) => {
  try {
    const userFound = await UserModel.findOne({ email: req.body.email });
    if (!userFound)
      return sendErrorResponse(
        req,
        res,
        StatusCodes.BAD_REQUEST,
        RESPONSE_MESSAGES.USER_NOT_FOUND
      );
    const UUID = uuidv4();
    const resetLink = `${process.env.MERGEN_WEB_APP_FRONTEND}/resetpassword/${UUID}`;
    sendMail(userFound.email, {
      html: `<p>Your link for resetting your password is:</p> 
                   <a href="${resetLink}" target="_blank">${resetLink}</a>`,
      subject: "Reset Password Email",
    });
    userFound.resetToken = UUID;
    userFound.expireTokenIn = Date.now() + 600000;
    await userFound.save();
    sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.OTP_SENT);
  } catch (err) {
    sendIntnernalServerError(res, err);
  }
};

exports.logoutUser = async (req, res) => {
  try {
    const { jti } = req.user;
    await UserToken.deleteOne({ jti });
    res.json({ message: "Logged out successfully" });
  } catch (err) {
    res.status(500).json({ error: "Logout failed" });
  }
};

exports.updateProfile = async (req, res) => {
  try {
    const { firstName, lastName, email, username } = req.body;
    const userAlreadyExists = await UserModel.findOne({
      $or: [{ username }, { email }],
      _id: { $ne: req.user.id },
    })
      .populate("goals")
      .lean()
      .exec();
    if (username && userAlreadyExists?.username == username)
      return sendErrorResponse(
        req,
        res,
        StatusCodes.BAD_REQUEST,
        RESPONSE_MESSAGES.USERNAME_ALREADY_EXISTS
      );
    if (email && userAlreadyExists?.email == email)
      return sendErrorResponse(
        req,
        res,
        StatusCodes.BAD_REQUEST,
        RESPONSE_MESSAGES.EMAIL_ALREADY_EXISTS
      );
    const user = await UserModel.findOneAndUpdate(
      { _id: req.user.id },
      req.body,
      { new: true }
    ).populate("goals");
    sendResponse(
      req,
      res,
      StatusCodes.OK,
      RESPONSE_MESSAGES.USER_UPDATED,
      user
    );
  } catch (err) {
    sendErrorResponse(
      req,
      res,
      StatusCodes.INTERNAL_SERVER_ERROR,
      RESPONSE_MESSAGES.INTERNAL_SERVER_ERROR
    );
  }
};

exports.sendMail = async (req, res) => {
  try {
    const { emailBody, subject, from, recipient } = req.body;
    if (!emailBody || !subject || !from || !recipient)
      return sendErrorResponse(
        req,
        res,
        StatusCodes.BAD_REQUEST,
        RESPONSE_MESSAGES.INVALID_REQUEST
      );
    sendMail(recipient, { html: emailBody, subject, from });
    sendResponse(
      req,
      res,
      StatusCodes.OK,
      RESPONSE_MESSAGES.USER_UPDATED,
      user
    );
  } catch (err) {
    sendErrorResponse(
      req,
      res,
      StatusCodes.INTERNAL_SERVER_ERROR,
      RESPONSE_MESSAGES.INTERNAL_SERVER_ERROR
    );
  }
};

exports.verifyUser = async (req, res) => {
  try {
    const user = await UserModel.findOne({ _id: req.user.id }, { password: 0 });
    if (!user)
      return sendErrorResponse(
        req,
        res,
        StatusCodes.BAD_REQUEST,
        RESPONSE_MESSAGES.USER_NOT_FOUND
      );
    const isAllowed = await canUseDeepResearch(req.user.id);
    sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.SUCCESS, {
      ...user._doc,
      ...isAllowed,
    });
  } catch (err) {
    console.log(err, "eroriuoiuoiuoiu");
    sendIntnernalServerError(res, err);
  }
};

exports.getAllUsers = async (req, res) => {
  try {
    const users = await UserModel.find({}).populate("goals").lean().exec();
    if (!users)
      return sendErrorResponse(
        req,
        res,
        StatusCodes.BAD_REQUEST,
        RESPONSE_MESSAGES.USER_NOT_FOUND
      );
    sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.SUCCESS, users);
  } catch (err) {
    sendIntnernalServerError(res, err);
  }
};

async function canUseDeepResearch(userId) {
  const subscription = await Models.Subscriptions.findOne({
    userId,
    status: "active",
  });
  const plan = subscription?.productId || process.env.FREE_PRODUCT;
  const limits = Constants.PLAN_LIMITS[plan];

  const now = new Date();

  // Start of the day (24-hour window)
  const startOfDay = new Date(now);
  startOfDay.setHours(0, 0, 0, 0);

  // Start of current hour
  const startOfHour = new Date(now);
  startOfHour.setMinutes(0, 0, 0);

  // Fetch all usage from today
  const recentUsage = await Models.DeepResearchUsage.find({
    userId,
    subscriptionId: subscription?.subscriptionId,
    usedAt: { $gte: startOfDay },
  });

  // Count usages in current 24 hours and current hour
  const dailyCount = recentUsage.length;
  const hourlyCount = recentUsage.filter((u) => u.usedAt >= startOfHour).length;

  console.log(
    {
      dailyCount,
      hourlyCount,
      startOfDay,
      startOfHour,
    },
    "Usage Stats"
  );

  if (dailyCount >= limits.daily) {
    return { allowed: false, reason: "daily_limit" };
  }

  if (hourlyCount >= limits.hourly) {
    return { allowed: false, reason: "hourly_limit" };
  }

  const usage = new Models.DeepResearchUsage({
    userId,
    subscriptionId: subscription?.subscriptionId,
    usedAt: now,
  });

  await usage.save();
  return { allowed: true };
}


// async function canUseDeepResearch(userId) {
//   const session = await mongoose.startSession();
//   session.startTransaction();

//   try {
//     const subscription = await Models.Subscriptions.findOne(
//       { userId, status: "active" },
//       null,
//       { session }
//     );

//     const plan = subscription?.productId || "prod_SLoeT8ESr5MUg9";
//     const limits = Constants.PLAN_LIMITS[plan];

//     const now = new Date();

//     const startOfDay = new Date(now);
//     startOfDay.setHours(0, 0, 0, 0);

//     const startOfHour = new Date(now);
//     startOfHour.setMinutes(0, 0, 0);

//     // Count daily usage
//     const dailyCount = await Models.DeepResearchUsage.countDocuments(
//       {
//         userId,
//         subscriptionId: subscription?.subscriptionId,
//         usedAt: { $gte: startOfDay },
//       },
//       { session }
//     );

//     if (dailyCount >= limits.daily) {
//       await session.abortTransaction();
//       session.endSession();
//       return { allowed: false, reason: "daily_limit" };
//     }

//     // Count hourly usage
//     const hourlyCount = await Models.DeepResearchUsage.countDocuments(
//       {
//         userId,
//         subscriptionId: subscription?.subscriptionId,
//         usedAt: { $gte: startOfHour },
//       },
//       { session }
//     );

//     if (hourlyCount >= limits.hourly) {
//       await session.abortTransaction();
//       session.endSession();
//       return { allowed: false, reason: "hourly_limit" };
//     }

//     // Record new usage atomically
//     const usage = new Models.DeepResearchUsage({
//       userId,
//       subscriptionId: subscription?.subscriptionId,
//       usedAt: now,
//     });

//     await usage.save({ session });
//     await session.commitTransaction();
//     session.endSession();

//     return { allowed: true };
//   } catch (err) {
//     await session.abortTransaction();
//     session.endSession();
//     console.error("Deep Research access error:", err);
//     return { allowed: false, reason: "error" };
//   }
// }
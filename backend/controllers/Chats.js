const { RESPONSE_MESSAGES } = require('../messages/responses');
const ChatModel = require('../models/Chats');
const { StatusCodes } = require('../statusCodes');
const { sendErrorResponse, sendResponse, sendIntnernalServerError } = require('../utils/responses');

exports.createChat = async (req, res) => {
    try {
        const { conversationId, title } = req.body;

        if (!conversationId || !title) {
            return sendErrorResponse(req, res, StatusCodes.BAD_REQUEST, RESPONSE_MESSAGES.INVALID_REQUEST)
        }

        const newChat = new ChatModel({
            conversationId,
            title
        });
        await newChat.save();
        sendResponse(req, res, StatusCodes.CREATED, RESPONSE_MESSAGES.CHAT_CREATED, newChat)
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
};

exports.updateChat = async (req, res) => {
    try {
        const { chatId } = req.params;
        const { title, summary, keywords } = req.body;

        if (!chatId || (!title && !summary && !keywords)) {
            return sendErrorResponse(req, res, StatusCodes.BAD_REQUEST, RESPONSE_MESSAGES.INVALID_REQUEST)
        }

        const updatedChat = await ChatModel.findByIdAndUpdate(chatId, {
            title,
            summary,
            keywords,
        }, { new: true });

        if (!updatedChat) {
            return sendErrorResponse(req, res, StatusCodes.NOT_FOUND, RESPONSE_MESSAGES.CHAT_NOT_FOUND)
        }
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.CHAT_UPDATED, updatedChat)
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
};

exports.getChatById = async (req, res) => {
    try {
        const { chatId } = req.params;
        if (!chatId) {
            return sendErrorResponse(req, res, StatusCodes.BAD_REQUEST, RESPONSE_MESSAGES.INVALID_REQUEST)
        }
        const chat = await ChatModel.findById(chatId);
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.CHAT_NOT_FOUND, chat)
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
}
exports.getChatsByConversationId = async (req, res) => {
    try {
        const { conversationId } = req.params;
        if (!conversationId) {
            return res.status(400).json({ code: 400, message: 'Invalid request' });
        }
        const chats = await ChatModel.find({ conversationId: conversationId })
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.CHAT_FOUND, chats)
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
}

exports.deleteChat = async (req, res) => {
    try {
        const { chatId } = req.params;
        if (!chatId) {
            return res.status(400).json({ code: 400, message: 'Invalid request' });
        }
        await ChatModel.deleteOne({ _id: chatId })
        res.status(204).send();
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
}
const { RESPONSE_MESSAGES } = require('../messages/responses');
const QuoteModel = require('../models/Quotes');
const { StatusCodes } = require('../statusCodes');
const { sendIntnernalServerError, sendResponse, sendErrorResponse } = require('../utils/responses');

exports.getAllQuotes = async (req, res) => {
    try {
        const quotes = await QuoteModel.find({ createdBy: req.user.id  })
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.QUOTE_FETCHED, quotes)
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
}

exports.getQuoteById = async (req, res) => {
    try {
        const quotes = await QuoteModel.find({ _id: req.params.quoteId  })
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.QUOTE_FETCHED, quotes)
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
}

exports.updateQuoteById = async (req, res) => {
    try {
        const quotes = await QuoteModel.findOneAndUpdate({ _id: req.params.quoteId  }, req.body, { new: true })
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.QUOTE_UPDATED, quotes)
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
}

exports.createQuotes = async (req, res) => {
    try {
        const { quote, author } = req.body;
        if (!author || !quote) {
            return res.status(400).json({ code: 400, message: 'Invalid request' });
        }
        const newQuote = new QuoteModel({
            author,
            quote,
            createdBy: req.user.id,
        });
        await newQuote.save();
        sendResponse(req, res, StatusCodes.CREATED, RESPONSE_MESSAGES.QUOTE_CREATED, newQuote)
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
}

exports.deleteQuotes = async (req, res) => {
    try {
        const quoteFOund = await QuoteModel.findOneAndDelete({ _id: req.params.quoteId })
        if(!quoteFOund) return sendErrorResponse(req, res, StatusCodes.NOT_FOUND, RESPONSE_MESSAGES.QUOTE_NOT_FOUND)
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.QUOTE_DELETED)
    } catch (err) {
        sendIntnernalServerError(res, err)
    }
}
const Todo = require("../models/Todo");
const { RESPONSE_MESSAGES } = require("../messages/responses");
const MessageModel = require("../models/Messages");
const editReport = require("../models/editReport");
const models = require("../models");
const { StatusCodes } = require("../statusCodes");
const {
  sendResponse,
  sendErrorResponse,
  sendIntnernalServerError,
} = require("../utils/responses");
const { default: axios } = require("axios");
const Chats = require("../models/Chats");

exports.createReport = async (req, res) => {
  try {
    let user = await models.Users.findOne({ _id: req.user.id }).populate(
      "goals"
    );
    const {
      chatId,
    } = req.body;
    const chat = await Chats.findById(chatId);

    const conversation = await models.Conversations.aggregate([
      { $match: { _id: chat.conversationId } },

      {
        $lookup: {
          from: "chats",
          let: { convoId: "$_id" },
          pipeline: [
            { $match: { $expr: { $eq: ["$conversationId", "$$convoId"] } } },
            { $sort: { createdAt: 1 } },
          ],
          as: "chats",
        },
      },
      {
        $lookup: {
          from: "messages",
          let: { chatIds: "$chats._id" },
          pipeline: [
            { $match: { $expr: { $in: ["$chatId", "$$chatIds"] } } },
            { $sort: { createdAt: 1 } },
            {
              $lookup: {
                from: "references",
                localField: "_id",
                foreignField: "messageId",
                as: "references",
              },
            },
            {
              $addFields: {
                references: {
                  $sortArray: {
                    input: "$references",
                    sortBy: { createdAt: 1 },
                  },
                },
              },
            },
          ],
          as: "allMessages",
        },
      },
      {
        $addFields: {
          chats: {
            $map: {
              input: "$chats",
              as: "chat",
              in: {
                $mergeObjects: [
                  "$$chat",
                  {
                    messages: {
                      $filter: {
                        input: "$allMessages",
                        as: "msg",
                        cond: { $eq: ["$$msg.chatId", "$$chat._id"] },
                      },
                    },
                  },
                ],
              },
            },
          },
        },
      },
      {
        $project: {
          allMessages: 0,
        },
      },
    ]);
    let data = conversation[0].chats[0].messages;

    const transformed = data.map((item) => {
      return {
        role: item.sender,
        content: item.content,
      };
    });

    const aiPayload = {
      user,
      conversation:{ messages:transformed},
    };

    const response = await axios.post(
      `${process.env.AI_SERVER}/api/v1/report`,
      aiPayload
    );
    if(response.data){
      const reportCreated = await models.Reports.create({ ...response.data, conversationId: chat.conversationId })
      response.data.reportId = reportCreated._id
    }
    sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.SUCCESS, response.data);
  } catch (error) {
    sendIntnernalServerError(res, error);
  }
};

exports.createEditReport = async (req, res) => {
  try {
    const { content, sender, metadata, mode = "", reportId } = req.body;
    console.log('content', req.body)
    if (!content || !sender) {
      return res.status(400).json({ code: 400, message: 'Invalid request' });
    }


    // let chat = await Chats.findById(chatId);
    // const newConversationId = chat?.conversationId || conversationId
    // if (!chat) { return sendErrorResponse(req, res, StatusCodes.NOT_FOUND, RESPONSE_MESSAGES.CHAT_NOT_FOUND) }

    let user = await models.Users.findOne({ _id: req.user.id }).populate("goals").lean();
    const newMessage = new models.editReport({
      // chatId,
      // conversationId: newConversationId,
      content,
      sender: "user",
      timestamp: new Date(),
      metadata: metadata || {},
      reportId
    });
    await newMessage.save();

      const promptModifiers = {
        remix: process.env.PROMPT_REMIX,
        longer: process.env.PROMPT_LONGER,
        shorter: process.env.PROMPT_SHORTER,
      };

      const modifierInstruction = promptModifiers[mode] || "";
      const modifiedPrompt = mode ? `${modifierInstruction}\n\n${content}` : content;

      const aiPayload = {
        user: { id: req.user.id, ...user },
        question: modifiedPrompt,
        conversation: [],
        session_id: reportId,
        // attachments: {
        //   image: images,
        //   file: files,
        //   audio: audios
        // }
        new_files: false
      }

      const response = await axios.post(`${process.env.AI_SERVER}/api/v1/agent/process`, aiPayload, {
        headers: {
          // 'Content-Type': 'application/json',
          'x-user-id': `${req.user.id}`
        }
      });

      
      const newMessageAI = await models.editReport.create({
        // chatId,
        content: response.data.execution_result.answer,
        // conversationId: newConversationId,
        sender: "ai",
        timestamp: new Date(),
        metadata: metadata || {},
        // versionNumber,
        reportId
      })

      res.status(201).json({
        data: {
          messageCreated: newMessage,
          // answer: "I am alright",
          aiMessageCreated: newMessageAI,
          // references: response.data.execution_result.citations
        }
      });

  } catch (err) {
    sendIntnernalServerError(res, err)
  }
};

exports.updateEditedReport = async(req, res, next) => {
  try{
    const { reportId, sections } = req.body
    const reportFound = await models.Reports.findOne({ _id: reportId })
    if(!reportFound) return res.status(404).json({ message: "Report not found" })
    const foundVersionCounts = await models.Reports.countDocuments({ reportId: reportId })
  
    const newReportVersion = {
      reportId: reportFound._id, 
      sections,
      versionNumber: +(foundVersionCounts || 0) + 1,
      title: reportFound.title, 
      subtitle: reportFound.subtitle
    }
    const newEditReport = await models.Reports.create(newReportVersion)
    sendResponse(req, res, 200, RESPONSE_MESSAGES.SUCCESS, newEditReport);
  }catch(err){
    sendIntnernalServerError(res, err);
  }
}
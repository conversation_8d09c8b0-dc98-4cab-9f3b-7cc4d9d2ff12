const { RESPONSE_MESSAGES } = require('../messages/responses');
const notificationModel = require('../models/Notifications');
const { StatusCodes } = require('../statusCodes');
const { sendIntnernalServerError, sendResponse, sendErrorResponse } = require('../utils/responses');

// Get all goals for the logged-in user
exports.createNotifications = io => async (req, res) => {
    try {
        const createNotofication = await notificationModel.create({ ...req.body, user: req.user.id });
        if (io) {
            io.emit(req.token?.toString(), createNotofication);
        }
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.NOTIFICATION_CREATED, createNotofication);
    } catch (err) {
        sendIntnernalServerError(res, err);
    }
}

exports.updateNotifications = async (req, res) => {
    try {
        const updatedNotofication = await notificationModel.findOneAndUpdate({ _id: req.params.id }, { ...req.body, user: req.user.id }, { new: true });
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.NOTIFICATION_STATUS_UPDATED, updatedNotofication);
    } catch (err) {
        sendIntnernalServerError(res, err);
    }
}

exports.getNotifications = async (req, res) => {
    try {
        const getNotifications = await notificationModel.find({ user: req.user.id });
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.NOTIFICATION_FETCHED, getNotifications);
    } catch (err) {
        sendIntnernalServerError(res, err);
    }
}
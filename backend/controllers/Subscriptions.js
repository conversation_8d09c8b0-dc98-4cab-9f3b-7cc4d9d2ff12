const { default: axios } = require('axios');
const { RESPONSE_MESSAGES } = require('../messages/responses');
const Models = require('../models');
const { StatusCodes } = require('../statusCodes');
const { sendErrorResponse, sendResponse, sendIntnernalServerError } = require('../utils/responses');
const stripe = require('../config/stripe');

exports.getAllSubscriptions = async (req, res) => {
  try {
    const subscriptions = await Models.Subscriptions.find({ userId: req.user.id, status: "active" }).exec()
    sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.SUCCESS, subscriptions)
  } catch (err) {
    console.log(err, "error during geting subscriptions", err)
    sendErrorResponse(req, res, StatusCodes.INTERNAL_SERVER_ERROR, RESPONSE_MESSAGES.INTERNAL_SERVER_ERROR)
  }
}

exports.createCheckoutSession = async (req, res) => {
  const { priceId } = req.body;

  try {
    const user = await Models.Users.findById(req.user.id);
    if (!user) return res.status(404).json({ error: 'User not found' });

    let customerId = user.stripeCustomerId;
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: `${user.firstName} ${user.lastName}`,
        metadata: { mongoUserId: user._id.toString() },
      });

      customerId = customer.id;
      user.stripeCustomerId = customerId;
      await user.save();
    }
    
    const session = await stripe.checkout.sessions.create({
      mode: 'subscription',
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      customer: customerId,
      success_url: `${process.env.DEEP_MERGEN_FRONTEND}/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.DEEP_MERGEN_FRONTEND}/cancel`,
    });
    res.json({ url: session.url, sessionId: session.id });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    res.status(500).json({ error: 'Unable to create session' });
  }
}
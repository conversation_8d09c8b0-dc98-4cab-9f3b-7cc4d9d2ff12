const { RESPONSE_MESSAGES } = require('../messages/responses');
const MessageModel = require('../models/Messages');
const userModel = require('../models/Users');
const GoalModel = require('../models/Goals');
const { StatusCodes } = require('../statusCodes');
const { sendIntnernalServerError, sendResponse, sendErrorResponse } = require('../utils/responses');
const Chats = require('../models/Chats');
const models = require('../models');
// const ConversationModel = require('../models/conversations');
const mongoose = require('mongoose');
require('dotenv').config();
const { default: axios } = require('axios');
const { uploadToS3 } = require('../utils/s3');

exports.createMessage = async (req, res) => {
  try {
    const { chatId, content, sender, metadata, mode = "" } = req.body;
    if (!chatId || !content || !sender) {
      return res.status(400).json({ code: 400, message: 'Invalid request' });
    }

    const images = req.files.filter(item => item.fieldname == 'image[]') || [];
    const files = req.files.filter(item => item.fieldname == 'file[]') || []
    const audios = req.files.filter(item => item.fieldname == 'audio[]') || []
    const uploaded = {
      image: [],
      file: [],
      audio: []
    };

    const chat = await Chats.findById(chatId);
    if (!chat) { return sendErrorResponse(req, res, StatusCodes.NOT_FOUND, RESPONSE_MESSAGES.CHAT_NOT_FOUND) }
    const uploadCategory = async (fileArray, type) => {
      return await Promise.all(
        fileArray.map(async (file) => {
          const key = `${req.user.id}/${chat.conversationId}/${file.originalname}`;
          await uploadToS3(file.buffer, key, file.mimetype);
          const fileUrl = `https://${process.env.AWS_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;
          return {
            name: file.originalname,
            url: fileUrl,
            type: file.mimetype
          };
        })
      );
    };

    uploaded.image = await uploadCategory(images, 'image');
    uploaded.file = await uploadCategory(files, 'file');
    uploaded.audio = await uploadCategory(audios, 'audio');

    let user = await userModel.findOne({ _id: req.user.id }).populate("goals").lean();
    const newMessage = new MessageModel({
      chatId,
      conversationId: chat.conversationId,
      content,
      sender,
      timestamp: new Date(),
      metadata: metadata || {},
    });
    await newMessage.save();

    const conversation = await models.Conversations.aggregate([
      { $match: { _id: chat.conversationId } },

      {
        $lookup: {
          from: 'chats',
          let: { convoId: '$_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$conversationId', '$$convoId'] } } },
            { $sort: { createdAt: 1 } }
          ],
          as: 'chats'
        }
      },
      {
        $lookup: {
          from: 'messages',
          let: { chatIds: '$chats._id' },
          pipeline: [
            { $match: { $expr: { $in: ['$chatId', '$$chatIds'] } } },
            { $sort: { createdAt: 1 } },
            {
              $lookup: {
                from: 'references',
                localField: '_id',
                foreignField: 'messageId',
                as: 'references'
              }
            },
            {
              $addFields: {
                references: {
                  $sortArray: { input: '$references', sortBy: { createdAt: 1 } }
                }
              }
            }
          ],
          as: 'allMessages'
        }
      },
      {
        $addFields: {
          chats: {
            $map: {
              input: '$chats',
              as: 'chat',
              in: {
                $mergeObjects: [
                  '$$chat',
                  {
                    messages: {
                      $filter: {
                        input: '$allMessages',
                        as: 'msg',
                        cond: { $eq: ['$$msg.chatId', '$$chat._id'] }
                      }
                    }
                  }
                ]
              }
            }
          }
        }
      },
      {
        $project: {
          allMessages: 0
        }
      }
    ]);
    if (!JSON.parse(req.body.isDeepResearch)) {
      let data = conversation[0].chats[0].messages
      //     const transformed = data.map(item => ({
      //   [item.sender]: item.content
      // }));
      const transformed = data.map(item => ({
        [item.sender === "ai" ? "assistant" : item.sender]: item.content
      }));

      const promptModifiers = {
        remix: process.env.PROMPT_REMIX,
        longer: process.env.PROMPT_LONGER,
        shorter: process.env.PROMPT_SHORTER,
      };

      const modifierInstruction = promptModifiers[mode] || "";

      const modifiedPrompt = mode ? `${modifierInstruction}\n\n${content}` : content;

      const aiPayload = {
        user: { id: req.user.id, ...user },
        question: modifiedPrompt,
        conversation: transformed,
        session_id: chat.conversationId,
        // attachments: {
        //   image: images,
        //   file: files,
        //   audio: audios
        // }
        new_files: !!(images.length || files.length || audios.length)
      }

      const response = await axios.post(`${process.env.AI_SERVER}/api/v1/agent/process`, aiPayload, {
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': `${req.user.id}`
        }
      });

      const newMessageAI = new MessageModel({
        chatId,
        content: response.data.execution_result.answer,
        conversationId: chat.conversationId,
        sender: "ai",
        timestamp: new Date(),
        metadata: metadata || {},
      });
      await newMessageAI.save();

      res.status(201).json({
        data: {
          messageCreated: newMessage,
          // answer: "I am alright",
          aiMessageCreated: newMessageAI,
          references: response.data.execution_result.citations
        }
      });
    } else {
      res.status(201).json({
        data: {
          messageCreated: newMessage,
          // answer: "I am alright",
          references: []
        }
      });
    }


  } catch (err) {
    sendIntnernalServerError(res, err)
  }
};

exports.updateMessage = async (req, res) => {
  try {
    const { messageId } = req.params;
    const { content } = req.body;

    if (!messageId || !content) {
      return res.status(400).json({ code: 400, message: 'Invalid request' });
    }

    const updatedMessage = await MessageModel.findByIdAndUpdate(messageId, {
      content,
      timestamp: new Date(),
    }, { new: true });

    if (!updatedMessage) {
      return res.status(404).json({ code: 404, message: 'Message not found' });
    }

    res.status(200).json(updatedMessage);
  } catch (err) {
    sendIntnernalServerError(res, err)
  }
};

exports.getMessagesByChatId = async (req, res) => {
  try {
    const { chatId } = req.params;

    if (!chatId) {
      return res.status(400).json({ code: 400, message: 'Invalid request' });
    }

    const messages = await MessageModel.find({ chatId }).sort({ timestamp: -1 });
    sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.MESSAGES_FOUND, messages)
  } catch (err) {
    sendIntnernalServerError(res, err)
  }
}

exports.getMessageById = async (req, res) => {
  try {
    const { messageId } = req.params;

    if (!messageId) {
      return res.status(400).json({ code: 400, message: 'Invalid request' });
    }

    const message = await MessageModel.findOne({ _id: chatId }).sort({ timestamp: -1 });

    sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.MESSAGE_FOUND, message)
  } catch (err) {
    sendIntnernalServerError(res, err)
  }
}
exports.deleteMessageById = async (req, res) => {
  try {
    const { messageId } = req.params;
    if (!messageId) {
      return res.status(400).json({ code: 400, message: 'Invalid request' });
    }
    const deletedMessage = await MessageModel.findByIdAndDelete(messageId);
    if (!deletedMessage) {
      return sendErrorResponse(req, res, StatusCodes.NOT_FOUND, RESPONSE_MESSAGES.MESSAGE_NOT_FOUND)
    }
    sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.MESSAGE_FOUND, {})
  } catch (err) {
    sendIntnernalServerError(res, err)
  }
}


function makeReferences(depth = 0, maxDepth = 2) {
  if (depth >= maxDepth) {
    return [];
  }
  return [
    {
      "Effective_start_date": new Date(),
      "Effective_end_date": (new Date()).setDate(new Date().getDate() + 10),
      "Summary": `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut vel
nisl tempus, placerat purus ullamcorper, condimentum nunc. In
vehicula mi sed nisi aliquet iaculis. Aliquam efficitur turpis
nec pretium pellentesque. Quisque placerat elit sit amet
volutpat euismod. Nunc purus metus, hendrerit a orci vitae,
rutrum aliquam libero. Pellentesque vitae scelerisque nunc.
Phasellus scelerisque libero in nibh mattis, non eleifend mauris
imperdiet. In quis pulvinar magna, id dapibus lorem. Quisque
sollicitudin magna ac cursus gravida. In ex arcu, blandit sed
placerat sed, eleifend quis orci. Vestibulum porta lectus a
ligula hendrerit, euismod. Nunc purus metus, hendrerit a orci
vitae, rutrum aliquam libero. Pellentesque vitae scelerisque
nunc. Phasellus scelerisque libero in nibh mattis, non eleifend
mauris imperdiet. In quis pulvinar magna, id dapibus lorem.
Quisque sollicitudin magna ac cursus gravida. In ex arcu,
blandit sed placerat sed, eleifend quis orci. Vestibulum porta
lectus a ligula hendrerit,`,
      "Category": "single document",
      "Keywords": [
        "Keyword1",
        "Keyword2",
        "Keyword3",
        "Keyword4",
        "Keyword5",
        "Keyword6",
        "Keyword7",
        "Keyword8",
        "Keyword9",
        "Keyword10",
      ],
      "Important_items": [
        `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut vel
nisl tempus, placerat purus ullamcorper, condimentum nunc.`,
        `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut vel
nisl tempus, placerat purus ullamcorper, condimentum nunc.`,
      ],
      references: makeReferences(depth + 1, maxDepth)
    },
    {
      "Effective_start_date": new Date(),
      "Effective_end_date": (new Date()).setDate(new Date().getDate() + 10),
      "Summary": `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut vel
nisl tempus, placerat purus ullamcorper, condimentum nunc. In
vehicula mi sed nisi aliquet iaculis. Aliquam efficitur turpis
nec pretium pellentesque. Quisque placerat elit sit amet
volutpat euismod. Nunc purus metus, hendrerit a orci vitae,
rutrum aliquam libero. Pellentesque vitae scelerisque nunc.
Phasellus scelerisque libero in nibh mattis, non eleifend mauris
imperdiet. In quis pulvinar magna, id dapibus lorem. Quisque
sollicitudin magna ac cursus gravida. In ex arcu, blandit sed
placerat sed, eleifend quis orci. Vestibulum porta lectus a
ligula hendrerit, euismod. Nunc purus metus, hendrerit a orci
vitae, rutrum aliquam libero. Pellentesque vitae scelerisque
nunc. Phasellus scelerisque libero in nibh mattis, non eleifend
mauris imperdiet. In quis pulvinar magna, id dapibus lorem.
Quisque sollicitudin magna ac cursus gravida. In ex arcu,
blandit sed placerat sed, eleifend quis orci. Vestibulum porta
lectus a ligula hendrerit,`,
      "Category": "set of document",
      "Keywords": [
        "Keyword1",
        "Keyword2",
        "Keyword3",
        "Keyword4",
        "Keyword5",
        "Keyword6",
        "Keyword7",
        "Keyword8",
        "Keyword9",
        "Keyword10",
      ],
      "Important_items": [
        `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut vel
nisl tempus, placerat purus ullamcorper, condimentum nunc.`,
        `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut vel
nisl tempus, placerat purus ullamcorper, condimentum nunc.`,
      ],
      references: makeReferences(depth + 1, maxDepth)
    },
    {
      "Effective_start_date": new Date(),
      "Effective_end_date": (new Date()).setDate(new Date().getDate() + 10),
      "Summary": `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut vel
nisl tempus, placerat purus ullamcorper, condimentum nunc. In
vehicula mi sed nisi aliquet iaculis. Aliquam efficitur turpis
nec pretium pellentesque. Quisque placerat elit sit amet
volutpat euismod. Nunc purus metus, hendrerit a orci vitae,
rutrum aliquam libero. Pellentesque vitae scelerisque nunc.
Phasellus scelerisque libero in nibh mattis, non eleifend mauris
imperdiet. In quis pulvinar magna, id dapibus lorem. Quisque
sollicitudin magna ac cursus gravida. In ex arcu, blandit sed
placerat sed, eleifend quis orci. Vestibulum porta lectus a
ligula hendrerit, euismod. Nunc purus metus, hendrerit a orci
vitae, rutrum aliquam libero. Pellentesque vitae scelerisque
nunc. Phasellus scelerisque libero in nibh mattis, non eleifend
mauris imperdiet. In quis pulvinar magna, id dapibus lorem.
Quisque sollicitudin magna ac cursus gravida. In ex arcu,
blandit sed placerat sed, eleifend quis orci. Vestibulum porta
lectus a ligula hendrerit,`,
      "Category": "chunk of document",
      "Keywords": [
        "Keyword1",
        "Keyword2",
        "Keyword3",
        "Keyword4",
        "Keyword5",
        "Keyword6",
        "Keyword7",
        "Keyword8",
        "Keyword9",
        "Keyword10",
      ],
      "Important_items": [
        `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut vel
nisl tempus, placerat purus ullamcorper, condimentum nunc.`,
        `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut vel
nisl tempus, placerat purus ullamcorper, condimentum nunc.`,
      ],
      references: makeReferences(depth + 1, maxDepth)
    }
  ].flatMap(obj =>
    Array(9).fill(null).map(() => ({ ...obj }))
  )
}
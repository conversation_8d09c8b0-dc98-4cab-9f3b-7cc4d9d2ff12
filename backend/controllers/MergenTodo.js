const Todo = require('../models/MergenTodo');
const { RESPONSE_MESSAGES } = require('../messages/responses');
const MessageModel = require('../models/Messages');
const { StatusCodes } = require('../statusCodes');
const { sendResponse, sendErrorResponse, sendIntnernalServerError } = require('../utils/responses');

exports.getTodos = async (req, res) => {
    try {
        const todos = await Todo.find({ userId: req.user.id }).sort({ createdAt: -1 });
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.SUCCESS, todos);
    } catch (error) {
        sendIntnernalServerError(res, error)
    }
};

exports.getTodo = async (req, res) => {
    try {
        const todo = await Todo.findById(req.params.id);
        
        if (!todo) {
            return sendErrorResponse(req, res, StatusCodes.NOT_FOUND, RESPONSE_MESSAGES.TODO_NOT_FOUND);
        }
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.TODOS_FETCHED, todo);
    } catch (error) {
        sendIntnernalServerError(res, error)
    }
};

exports.createTodo = async (req, res) => {
    try {
        const todo = await Todo.create({ ...req.body, userId: req.user.id });
        sendResponse(req, res, StatusCodes.CREATED, RESPONSE_MESSAGES.TODO_CREATED, todo);
    } catch (error) {
        sendIntnernalServerError(res, error)
    }
};

exports.updateTodo = async (req, res) => {
    try {
        const todo = await Todo.findByIdAndUpdate(req.params.id, req.body, {
            new: true,
            runValidators: true
        });

        if (!todo) {
            return sendErrorResponse(req, res, StatusCodes.NOT_FOUND, RESPONSE_MESSAGES.TODO_NOT_FOUND);
        }

        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.TODO_UPDATED, todo);
    } catch (error) {
        sendIntnernalServerError(res, error)
    }
};

exports.deleteTodo = async (req, res) => {
    try {
        const todo = await Todo.findByIdAndDelete(req.params.id);

        if (!todo) {
            return sendErrorResponse(req, res, StatusCodes.NOT_FOUND, RESPONSE_MESSAGES.TODO_NOT_FOUND);
        }
        sendResponse(req, res, StatusCodes.OK, RESPONSE_MESSAGES.TODO_DELETED, todo);
    } catch (error) {
        sendIntnernalServerError(res, error)
    }
}; 
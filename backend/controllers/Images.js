const AWS = require('aws-sdk');
const Image = require('../models/Image');
const { uploadToS3, getAllS3Objects, deleteObjectFromS3 } = require('../utils/s3');
const Images = require("../models/Image")
const s3 = new AWS.S3({ region: process.env.AWS_REGION });
const BUCKET = process.env.AWS_BUCKET_NAME;

exports.uploadFile = async (req, res) => {
  try {
    const file = req.file;
    if (!file) return res.status(400).json({ error: "No file uploaded" });

    const key = `uploads/${Date.now()}_${file.originalname.replace(/[^a-zA-Z0-9._-]/g, '')}`;

    await uploadToS3(file.buffer, key, file.mimetype);
    const fileUrl = `https://${process.env.AWS_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;
    await Images.create({ filename: file.originalname.replace(/[^a-zA-Z0-9._-]/g, ''), url: fileUrl, uploadedBy: req.user.id });

    res.status(200).json({ message: "File uploaded successfully", url: fileUrl });
  } catch (error) {
    console.error("S3 Upload Error:", error);
    res.status(500).json({ error: "Failed to upload file" });
  }
};

// Get all images
exports.getImages = async (req, res) => {
  try {
    const images = await Image.find().sort({ uploadedAt: -1 });
    res.json(images);
  } catch (err) {
    res.status(500).json({ error: 'Error fetching images' });
  }
};

exports.getAllImages = async (req, res) => {
  try {
    const result = await Images.find({ uploadedBy: req.user.id }).sort({ uploadedAt: -1 });
    res.status(200).json(result);
  } catch (err) {
    console.error("Error listing files:", err);
    res.status(500).json({ error: "Could not list files" });
  }
};


// Delete image from S3 and DB
exports.deleteImage = async (req, res) => {
  try {
    const { url } = req.query;

    if (!url) {
      return res.status(400).json({ error: "URL is required" });
    }

    const bucket = process.env.AWS_BUCKET_NAME;
    const region = process.env.AWS_REGION;
    
    const baseUrl = `https://${bucket}.s3.${region}.amazonaws.com/`;

    if (!url.startsWith(baseUrl)) {
      return res.status(400).json({ error: "Invalid S3 URL" });
    }

    const key = decodeURIComponent(url.replace(baseUrl, ""));

    await Images.deleteOne({ url, uploadedBy: req.user.id });
    if (process.env.DEFAULT_GALLERY_PIC !== url) {
      await deleteObjectFromS3(key);
    }

    res.status(200).json({ message: "File deleted successfully", key });
  } catch (err) {
    console.error("Error deleting file:", err);
    res.status(500).json({ error: "Could not delete file" });
  }
};


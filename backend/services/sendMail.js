  const nodemailer = require("nodemailer");

  const mailerService = async (email, { html, subject }) => {
    try {
      let transporter = nodemailer.createTransport({
        host: "smtp.mail.us-east-1.awsapps.com",
        port: 465,
        secure: true,
        auth: {
          user: process.env.EMAIL,
          pass: process.env.EMAIL_PASS,
        },
        // tls: {
        //   rejectUnauthorized: false, // Accept self-signed certificates
        // },
      });

      await transporter.sendMail({
        from: ` <${process.env.EMAIL}>`,
        to: email,
        subject: subject,
        html,
      });

    } catch (err) {
      console.error("Error sending email:", err);
    }
  };

  module.exports = mailerService;

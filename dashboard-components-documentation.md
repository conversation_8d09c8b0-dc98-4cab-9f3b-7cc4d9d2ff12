# Mergen AI Dashboard Components Documentation

## Overview

The Mergen AI Dashboard is a React-based chat interface with conversation management, search functionality, and results display. It's built with TypeScript, Tailwind CSS, Framer Motion for animations, and Ant Design components.

## Architecture Overview

### Component Hierarchy
```
Dashboard (Main Container)
├── HeaderSection (Navigation & User Menu)
├── Routes
│   ├── DashboardSection (Main Content Area)
│   │   ├── HistoryDrawer (Conversation Sidebar)
│   │   ├── ListViewSection (Results Display)
│   │   │   └── ListCards (Individual Result Cards)
│   │   ├── CardSectionArea (Alternative Masonry View)
│   │   └── FooterSection (Chat Interface)
│   │       ├── SearchBar (Input Component)
│   │       └── EmailSection (Email Modal)
│   └── ReportSection (Report Generation)
```

## Core Components

### 1. Dashboard.tsx
**Location**: `frontend/src/components/dashboard/Dashboard.tsx`

**Purpose**: Main orchestrator component that manages routing and overall layout.

**Key Features**:
- Handles routing between dashboard and report views
- Manages global state for conversations, messages, and references
- Integrates with multiple services (conversation, chat, message, reference)

**Props**: None (root component)

**State Management**:
- `isListView`: Boolean to toggle between list and card views
- `inputValue`: Current search input
- `openEmailSection`: Email modal visibility
- `openHistoryView`: History drawer visibility
- `allConversations`: Array of user conversations
- `activeConversation`: Currently selected conversation ID
- `messages`: Array of chat messages
- `referenceData`: Search results data

### 2. HeaderSection.tsx
**Location**: `frontend/src/components/dashboard/HeaderSection.tsx`

**Purpose**: Top navigation bar with logo and user controls.

**Key Features**:
- Responsive logo placement (centered on desktop, left on mobile)
- Settings/logout functionality
- Mobile menu toggle
- Navigation to search page

**Props**: None

**Components Used**:
- Custom logo component
- Settings icon
- Mobile menu (MobileMenuView)

### 3. FooterSection.tsx
**Location**: `frontend/src/components/dashboard/FooterSection.tsx`

**Purpose**: Bottom chat interface container with input and controls.

**Key Features**:
- Search input with auto-resize
- Email functionality
- Message navigation (previous/next)
- Integration with SearchBar component

**Props**:
```typescript
interface FooterSectionProps {
  messages: Message[];
  reply: string;
  handleSearch: () => void;
  setInputValue: (value: string) => void;
  inputValue: string;
  openEmailSection: boolean;
  setOpenEmailSection: (value: boolean) => void;
  activeChat: string;
  handleNext: () => void;
  handlePrevious: () => void;
  activePairIndex: number;
  maxPair: number;
}
```

### 4. ListViewSection.tsx
**Location**: `frontend/src/components/dashboard/ListViewSection.tsx`

**Purpose**: Container for displaying search results in list format.

**Key Features**:
- Maps over reference data to render ListCards
- Handles empty state display
- Manages backup data state

**Props**:
```typescript
interface ListViewSectionProps {
  referenceData: ReferenceContent[];
}
```

### 5. ListCards.tsx
**Location**: `frontend/src/components/dashboard/ListCards.tsx`

**Purpose**: Individual result card component displaying search results.

**Key Features**:
- Displays summary, keywords, and key points
- Modal for expanded view
- Interactive elements for detailed information
- Responsive design

**Props**:
```typescript
interface ListCardsProps {
  data: {
    summary: string;
    keywords: string[];
    key_points: string[];
  };
  setBackUpData?: (data: any) => void;
  referenceData?: any[];
  backupData?: any[];
}
```

**Data Structure**:
```typescript
interface ReferenceContent {
  summary: string;
  keywords: string[];
  key_points: string[];
}
```

### 6. HistoryDrawer.tsx
**Location**: `frontend/src/components/dashboard/HistoryDrawer.tsx`

**Purpose**: Animated sidebar for conversation history management.

**Key Features**:
- Conversation list with search functionality
- Conversation editing and deletion
- Date formatting and organization
- Smooth animations with Framer Motion

**Props**:
```typescript
interface HistoryDrawerProps {
  setOpenHistoryView: (open: boolean) => void;
  allConversations: Conversation[];
  activeConversation: string;
  setActiveConversation: (id: string) => void;
  getAllConversations: () => void;
  setOpenEmailSection: (open: boolean) => void;
  activeChat: string;
}
```

### 7. CardSectionArea.tsx
**Location**: `frontend/src/components/dashboard/CardSectionArea.tsx`

**Purpose**: Alternative masonry-style view for displaying content cards.

**Key Features**:
- Responsive masonry layout (1-4 columns based on screen size)
- Interactive tooltips with detailed information
- Modal for expanded card view
- Animated transitions

**Props**: None (uses internal state)

### 8. EmailSection.tsx
**Location**: `frontend/src/components/dashboard/emailSection/EmailSection.tsx`

**Purpose**: Modal component for sending emails with chat context.

**Key Features**:
- Email form with to, subject, and message fields
- Real-time preview panel
- Glass morphism design
- Integration with mailto functionality

**Props**:
```typescript
interface EmailSectionProps {
  openEmailSection: boolean;
  setOpenEmailSection: (open: boolean) => void;
  activeChat: string;
}
```

## Services Layer

### 1. conversationService.ts
**Purpose**: API calls for conversation management

**Key Functions**:
- `createConversation(payload)`: Create new conversation
- `getConversation(params)`: Get user conversations
- `updateConversation(id, updates)`: Update conversation
- `deleteConversation(id)`: Delete conversation
- `searchConversations(query)`: Search conversations

### 2. chatService.ts
**Purpose**: API calls for chat management

**Key Functions**:
- `createChat(payload)`: Create new chat
- `getChatByConversationId(id)`: Get chats by conversation
- `getChatById(id)`: Get specific chat
- `updateChat(id, updates)`: Update chat
- `deleteChat(id)`: Delete chat

### 3. messageService.ts
**Purpose**: API calls for message management with file upload support

**Key Functions**:
- `createMessage(payload)`: Create message with attachments
- `getMessageByChatId(id)`: Get messages by chat
- `updateMessage(id, updates)`: Update message
- `deleteMessage(id)`: Delete message

**Features**:
- FormData handling for file uploads
- Support for images, audio, documents, and URLs
- Axios interceptors for authentication

### 4. referenceService.ts
**Purpose**: API calls for reference/search results management

**Key Functions**:
- `createReference(payload)`: Create reference
- `getReferencesByMessageId(id)`: Get references by message
- `updateReference(id, updates)`: Update reference
- `deleteReference(id)`: Delete reference

## Custom Hooks

### 1. useUser
**Purpose**: User state management and authentication

**Returns**:
```typescript
{
  user: User | null;
  setUser: (user: User | null) => void;
  loadUserFromStorage: () => void;
}
```

### 2. useLoader
**Purpose**: Global loading state management

**Returns**:
```typescript
{
  loading: boolean;
  setLoading: (loading: boolean) => void;
}
```

### 3. useWindowResize
**Purpose**: Window resize detection for responsive behavior

**Returns**: Window dimensions and resize handlers

## State Management

### Context Providers
1. **UserProvider**: Manages user authentication state
2. **LoaderProvider**: Manages global loading states

### Local State Patterns
- Component-level state for UI interactions
- Prop drilling for shared state between related components
- Service layer for API state management

## Data Flow

### 1. Search Flow
```
User Input (FooterSection) 
→ handleSearch() 
→ createMessage() service call
→ API processes search
→ getReferencesByMessageId() 
→ Update referenceData state
→ ListViewSection renders results
→ ListCards display individual results
```

### 2. Conversation Flow
```
User selects conversation (HistoryDrawer)
→ setActiveConversation()
→ getChatByConversationId()
→ getMessageByChatId()
→ Update messages state
→ FooterSection displays chat history
```

### 3. Email Flow
```
User clicks email button (FooterSection)
→ setOpenEmailSection(true)
→ EmailSection modal opens
→ User fills form
→ handleSendEmail()
→ mailto: link or API call
```

## Styling System

### Technologies Used
- **Tailwind CSS**: Utility-first CSS framework
- **Custom CSS**: Component-specific styles
- **Framer Motion**: Animation library

### Key Design Patterns
- Glass morphism effects with backdrop blur
- Responsive grid systems (1-4 columns)
- Custom scrollbar styling
- Shadow and border radius consistency
- Color scheme with dark/light mode support

### Animation Patterns
- Page transitions with Framer Motion
- Hover effects on interactive elements
- Modal entrance/exit animations
- Drawer slide animations

## File Structure
```
frontend/src/components/dashboard/
├── Dashboard.tsx              # Main orchestrator
├── HeaderSection.tsx          # Navigation
├── FooterSection.tsx          # Chat interface
├── ListViewSection.tsx        # Results container
├── ListCards.tsx              # Result cards
├── HistoryDrawer.tsx          # Conversation sidebar
├── CardSectionArea.tsx        # Masonry view
├── MobileMenuView.tsx         # Mobile navigation
└── emailSection/
    └── EmailSection.tsx       # Email modal
```

## Dependencies

### Core Dependencies
- React 18+ with TypeScript
- React Router DOM for routing
- Framer Motion for animations
- Ant Design for UI components
- Axios for API calls

### Styling Dependencies
- Tailwind CSS 4.0
- Custom fonts (Inter, Charter, Tw-Cen-MT)

## Usage Examples

### Basic Setup
```typescript
import Dashboard from './components/dashboard/Dashboard';
import { UserProvider } from './context/UserContext';
import { LoaderProvider } from './context/LoaderContext';

function App() {
  return (
    <UserProvider>
      <LoaderProvider>
        <Dashboard />
      </LoaderProvider>
    </UserProvider>
  );
}
```

### Using Individual Components
```typescript
import ListCards from './components/dashboard/ListCards';

const MyComponent = () => {
  const sampleData = {
    summary: "Sample summary text",
    keywords: ["keyword1", "keyword2"],
    key_points: ["Point 1", "Point 2"]
  };

  return (
    <ListCards 
      data={sampleData}
      setBackUpData={setBackUpData}
      referenceData={referenceData}
      backupData={backupData}
    />
  );
};
```

## Performance Considerations

### Optimization Techniques
- Lazy loading for large conversation lists
- Memoization for expensive calculations
- Debounced search input
- Virtual scrolling for large result sets
- Image optimization and lazy loading

### Bundle Size Management
- Tree shaking for unused code
- Code splitting by route
- Dynamic imports for heavy components
- Optimized asset loading

## Testing Strategy

### Component Testing
- Unit tests for individual components
- Integration tests for component interactions
- Mock API responses for service testing

### E2E Testing
- User flow testing for search functionality
- Conversation management testing
- Email functionality testing
- Responsive design testing

## API Integration

### Authentication Flow
```typescript
// Request interceptor adds auth token
ProtectedAPI.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor handles auth errors
ProtectedAPI.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('authToken');
      window.location.href = '/';
    }
    return Promise.reject(error);
  }
);
```

### Error Handling Patterns
```typescript
// Service layer error handling
const handleApiCall = async () => {
  try {
    setLoading(true);
    const response = await apiService.getData();
    setData(response.data);
  } catch (error) {
    console.error('API Error:', error);
    // Handle specific error types
    if (error.response?.status === 404) {
      setError('Data not found');
    } else {
      setError('Something went wrong');
    }
  } finally {
    setLoading(false);
  }
};
```

## Component Communication Patterns

### Parent-Child Communication
```typescript
// Parent passes data and handlers to child
<ListViewSection
  referenceData={referenceData}
  onItemSelect={handleItemSelect}
/>

// Child receives props and calls handlers
const ListViewSection = ({ referenceData, onItemSelect }) => {
  return (
    <div>
      {referenceData.map(item => (
        <ListCards
          key={item.id}
          data={item}
          onClick={() => onItemSelect(item)}
        />
      ))}
    </div>
  );
};
```

### Sibling Communication via Parent
```typescript
// Dashboard manages state shared between siblings
const Dashboard = () => {
  const [activeConversation, setActiveConversation] = useState('');
  const [messages, setMessages] = useState([]);

  return (
    <>
      <HistoryDrawer
        activeConversation={activeConversation}
        setActiveConversation={setActiveConversation}
      />
      <FooterSection
        messages={messages}
        activeConversation={activeConversation}
      />
    </>
  );
};
```

## Responsive Design Strategy

### Breakpoint System
```css
/* Tailwind CSS breakpoints used */
sm: 640px   /* Small devices */
md: 768px   /* Medium devices */
lg: 1024px  /* Large devices */
xl: 1280px  /* Extra large devices */
```

### Mobile-First Approach
```typescript
// Example responsive component
const HeaderSection = () => {
  return (
    <div className="flex justify-between px-6 sm:px-14 py-8 sm:py-11">
      {/* Mobile: left-aligned, Desktop: centered */}
      <div className="flex-1 flex justify-start lg:justify-center">
        <Logo />
      </div>

      {/* Hidden on mobile, visible on desktop */}
      <div className="lg:flex justify-end gap-2.5 items-center flex-1 hidden">
        <SettingsIcon />
      </div>
    </div>
  );
};
```

### Adaptive Layouts
```typescript
// CardSectionArea uses responsive columns
<div className="columns-1 gap-0 md:columns-2 lg:columns-3 xl:columns-4">
  {/* Content adapts to screen size */}
</div>
```

## Security Considerations

### Data Sanitization
```typescript
// Sanitize user input before API calls
const sanitizeInput = (input: string) => {
  return input.trim().replace(/[<>]/g, '');
};

const handleSearch = () => {
  const sanitizedInput = sanitizeInput(inputValue);
  if (sanitizedInput.length > 0) {
    performSearch(sanitizedInput);
  }
};
```

### Token Management
```typescript
// Secure token storage and validation
const isTokenValid = (token: string) => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp > Date.now() / 1000;
  } catch {
    return false;
  }
};

// Auto-logout on token expiry
useEffect(() => {
  const token = localStorage.getItem('authToken');
  if (token && !isTokenValid(token)) {
    handleLogout();
  }
}, []);
```

## Accessibility Features

### Keyboard Navigation
```typescript
// Example keyboard event handling
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    handleSearch();
  } else if (event.key === 'Escape') {
    setOpenEmailSection(false);
  }
};
```

### ARIA Labels and Roles
```typescript
// Accessible component structure
<button
  aria-label="Open conversation history"
  role="button"
  onClick={() => setOpenHistoryView(true)}
>
  <HistoryIcon />
</button>

<div
  role="dialog"
  aria-modal="true"
  aria-labelledby="email-modal-title"
>
  <EmailSection />
</div>
```

## Troubleshooting Guide

### Common Issues

#### 1. Components Not Rendering
**Problem**: Components appear blank or don't render
**Solution**:
- Check if required props are passed correctly
- Verify data structure matches expected interface
- Check console for TypeScript errors

#### 2. API Calls Failing
**Problem**: Network requests return errors
**Solution**:
- Verify API endpoint URLs in environment variables
- Check authentication token validity
- Inspect network tab for detailed error messages

#### 3. Styling Issues
**Problem**: Components don't look as expected
**Solution**:
- Ensure Tailwind CSS is properly configured
- Check for conflicting CSS classes
- Verify responsive breakpoints

#### 4. State Not Updating
**Problem**: Component state doesn't reflect changes
**Solution**:
- Check if state setters are called correctly
- Verify useEffect dependencies
- Ensure immutable state updates

### Debug Tools

#### React Developer Tools
```typescript
// Add display names for better debugging
Dashboard.displayName = 'Dashboard';
ListCards.displayName = 'ListCards';
```

#### Console Logging
```typescript
// Strategic logging for debugging
useEffect(() => {
  console.log('Dashboard state updated:', {
    activeConversation,
    messagesCount: messages.length,
    referenceDataCount: referenceData.length
  });
}, [activeConversation, messages, referenceData]);
```

## Performance Monitoring

### Key Metrics to Track
- Component render times
- API response times
- Bundle size and load times
- Memory usage patterns
- User interaction responsiveness

### Optimization Techniques
```typescript
// Memoization for expensive calculations
const processedData = useMemo(() => {
  return referenceData.map(item => ({
    ...item,
    processedSummary: processSummary(item.summary)
  }));
}, [referenceData]);

// Debounced search to reduce API calls
const debouncedSearch = useCallback(
  debounce((query: string) => {
    performSearch(query);
  }, 300),
  []
);
```

## Future Enhancements

### Planned Features
- Real-time chat updates with WebSocket
- Advanced search filters and sorting
- Conversation export functionality
- Improved mobile experience
- Voice input support
- Dark/light theme toggle
- Accessibility improvements

### Technical Improvements
- State management with Redux Toolkit
- Better error handling and retry logic
- Offline functionality with service workers
- Performance monitoring and analytics
- SEO optimization for public pages
- Automated testing coverage
- Component library extraction

## Contributing Guidelines

### Code Standards
- Use TypeScript for all new components
- Follow existing naming conventions
- Add proper JSDoc comments for complex functions
- Ensure responsive design for all components
- Write unit tests for new features

### Pull Request Process
1. Create feature branch from main
2. Implement changes with tests
3. Update documentation if needed
4. Submit PR with detailed description
5. Address review feedback
6. Merge after approval

### Development Setup
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm test

# Build for production
npm run build
```

import { ProtectedAPI } from "./axiosInstance";

const createTodoList = async (description:string,completed:boolean)=> {
    const response = await ProtectedAPI.post("/todos", { description,completed });
    return response.data;
};

const getTodoList = async ()=> {
    const response = await ProtectedAPI.get(`/todos`);
    return response.data;
};

const deleteTodoById = async (id:string)=> {
    const response = await ProtectedAPI.delete(`/todos/${id}`);
    return response.data;
};

const updateTodoById = async (description:string,completed:boolean,id:string)=> {
    const response = await ProtectedAPI.put(`/todos/${id}`,{description,completed});
    return response.data;
};

const createAiTodoList = async (description:string,completed:boolean)=> {
    const response = await ProtectedAPI.post("/mergen-todos", { description,completed });
    return response.data;
};

const getAiTodoList = async ()=> {
    const response = await ProtectedAPI.get(`/mergen-todos`);
    return response.data;
};

const deleteAiTodoById = async (id:string)=> {
    const response = await ProtectedAPI.delete(`/mergen-todos/${id}`);
    return response.data;
};

const updateAiTodoById = async (description:string,completed:boolean,id:string)=> {
    const response = await ProtectedAPI.put(`/mergen-todos/${id}`,{description,completed});
    return response.data;
};




export { getTodoList,createTodoList,deleteTodoById,updateTodoById,updateAiTodoById,deleteAiTodoById,getAiTodoList,createAiTodoList };

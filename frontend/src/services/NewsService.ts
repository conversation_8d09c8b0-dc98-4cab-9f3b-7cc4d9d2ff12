import { ProtectedAPI } from "./axiosInstance";

const getNews = async () => {
  const response = await ProtectedAPI.get(`/news`);
  return response.data;
};

const updateNews = async ({
  country,
  category,
  language,
}: {
  country: string;
  category: string;
  language: string;
}) => {
  const response = await ProtectedAPI.post(`/news`, {
    country,
    category,
    language,
  });
  return response.data;
};

const getNewsType = async () => {
    const response = await ProtectedAPI.get(`/news/types`);
    return response.data;
  };

export { getNews, updateNews,getNewsType };

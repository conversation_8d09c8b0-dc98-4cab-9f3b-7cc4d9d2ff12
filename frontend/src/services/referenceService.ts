import { ProtectedAPI } from "./axiosInstance";

// type createReferenceType = {
//     messageId:string
//     content:[]
//     type:string
//     sourceUrl:string
// }

const createReference = async ({messageId,content }:any)=> {
    const response = await ProtectedAPI.post("/references", { messageId,content });
    return response.data;
};

const getReferencesByMessageId = async (messageId:string)=> {
    const response = await ProtectedAPI.get(`/references/message/${messageId}`);
    return response.data;
};

export { createReference,getReferencesByMessageId };

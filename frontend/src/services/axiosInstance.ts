import axios, { AxiosInstance } from "axios";

// const BASE_URL = "http://192.168.1.58:5000/api/v1";
// const BASE_URL = "https://1ad6-122-160-153-248.ngrok-free.app/api/v1";
// const BASE_URL = "https://o09w40hz05.execute-api.ap-south-1.amazonaws.com/api/v1";
const BASE_URL = "https://backend.mergenai.io/api/v1";

export const AuthAPI: AxiosInstance = axios.create({
    baseURL: BASE_URL,
    headers: {
        "Content-Type": "application/json",
    },
});

export const ProtectedAPI: AxiosInstance = axios.create({
    baseURL: BASE_URL,
    headers: {
        "Content-Type": "application/json",
    },
});

ProtectedAPI.interceptors.request.use((config) => {
    const token = localStorage.getItem("authToken");
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

ProtectedAPI.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response && error.response.status === 401) {
            localStorage.removeItem("authToken");
            window.location.href = "/";
        }
        return Promise.reject(error);
    }
);

export default AuthAPI;

import { AuthAPI, ProtectedAPI } from "./axiosInstance";

type registerType = {
    username: string,
    email: string,
    password: string,
    firstName: string,
    lastName: string
  }

type loginType = {
    username:string,
    password:string
}

type forgetPasswordType = {
    email:string,
}

type resetPasswordType = {
    currentPassword:string,
    newPassword:string,
    token:string | undefined
}

// User Login
const login = async ({username, password}:loginType)=> {
    const response = await AuthAPI.post("/users/login", { username, password });
    if(response?.data?.token){
        localStorage.setItem("authToken", response?.data?.token);
    }
    return response.data;
};

// User Signup
const register = async ({username,password,email,firstName,lastName}:registerType ) => {
    const response = await AuthAPI.post("/users/register", {username,password,email,firstName,lastName});
    localStorage.setItem("authToken", response?.data?.data?.token);
    return response.data;
};

const forgetPassword = async ({email}: forgetPasswordType) => {
    const response = await AuthAPI.post("/users/forget-password", {email});
    return response.data;
};

const resetPassword = async ({currentPassword,newPassword,token}: resetPasswordType) => {
    const response = await AuthAPI.post("/users/change-password", {currentPassword,newPassword,resetToken:token});
    localStorage.setItem("authToken", response?.data?.data?.token);
    return response.data;
};

const logOut = async () => {
    const response = await ProtectedAPI.post("/users/logout");
    return response.data;
};

export { login, register,logOut,forgetPassword,resetPassword };
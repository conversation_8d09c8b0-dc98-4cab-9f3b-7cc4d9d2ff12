import { ProtectedAPI } from "./axiosInstance";

type createChatType = {
   conversationId:string
   title:string
}

const createChat = async ({title,conversationId}:createChatType)=> {
    const response = await ProtectedAPI.post("/chats", { title,conversationId });
    return response.data;
};
const getChatByConversationId = async (conversationId:string)=> {
    const response = await ProtectedAPI.get(`/chats/conversation/${conversationId}`);
    return response.data;
};

export { createChat,getChatByConversationId };

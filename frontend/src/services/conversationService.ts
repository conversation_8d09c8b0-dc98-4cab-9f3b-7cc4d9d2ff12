import { ProtectedAPI } from "./axiosInstance";

type createConversationType = {
   title:string
}

const createConversation = async ({title}:createConversationType)=> {
    const response = await ProtectedAPI.post("/conversations", { title });
    return response.data;
};

const updateConversationById = async ({_id}:{_id:string},title:string)=> {
    const response = await ProtectedAPI.put(`/conversations/${_id}`, { title });
    return response.data;
};

const deleteConversationById = async (id:string)=> {
    const response = await ProtectedAPI.delete(`/conversations/${id}`);
    return response.data;
};

const getConversation = async ()=> {
    const response = await ProtectedAPI.get(`/conversations?isDeepResearch=${false}`);
    return response.data;
};

export { createConversation,getConversation,updateConversationById,deleteConversationById };

import { ProtectedAPI } from "./axiosInstance";

// type createMessageType = {
//     chatId:string
//     content:string
//     sender:string
//     attachedFiles:object
// }


// type MediaFile = {
//   file: File | Blob;
// };

// type AttachedFilesType = {
//   image?: MediaFile[];
//   audio?: MediaFile[];
//   url?: { name: string }[];  // assuming URLs have a `name` field
//   file?: MediaFile[];
// };

// type CreateMessageType = {
//   chatId: string;
//   content: string;
//   sender: string;
//   attachedFiles: AttachedFilesType;
// };

const createMessage = async ({chatId,content,sender,attachedFiles}:any)=> {
    const formData = new FormData();
    // formData.append("chatId", chatId);
    // formData.append("content", content);
    // formData.append("sender", sender);
    // formData.append("attachedFiles", JSON.stringify(attachedFiles));

    formData.append("chatId", chatId);
  formData.append("content", content);
  formData.append("sender", sender);
  formData.append("isDeepResearch", JSON.stringify(false)); 

  // Append images
  attachedFiles.image?.forEach((file: any) => {
    formData.append(`image[]`, file.file);
  });

  // Append audio
  attachedFiles.audio?.forEach((file: any) => {
    formData.append("audio[]", file.file);
  });

  // Append URLs (as strings)
  attachedFiles.url?.forEach((url: any) => {
    formData.append("url[]", url.name);
  });

  // Append documents or other files
  attachedFiles.file?.forEach((file:any, ) => {
    formData.append("file[]", file.file);
  });


    // formData.append("file", file);
    const response = await ProtectedAPI.post("/messages", formData, {
    headers:{
        "Content-Type": "multipart/form-data", 
    }
  });
    return response.data;
};

const getMessageByChatId = async (chatId:string)=> {
    const response = await ProtectedAPI.get(`/messages/chat/${chatId}`);
    return response.data;
};

export { createMessage,getMessageByChatId };

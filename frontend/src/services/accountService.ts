import { ProtectedAPI } from "./axiosInstance";

const updateUserInfo = async (user: any) => {
  const response = await ProtectedAPI.put(`users/update-profile`, user);
  return response.data;
};

export { updateUserInfo };

export const updatePassword = async (data: {
  oldPassword: string;
  newPassword: string;
}) => {
  const response = await ProtectedAPI.put(`/users/update-password`, data);
  localStorage.setItem("authToken", response?.data?.data?.token);
  return response.data;
};

export const addQuotes = async (payload: { quote: string; author: string }) => {
  const response = await ProtectedAPI.post(`/quotes`, payload);
  return response.data;
};

export const deleteQuote = async (id: string) => {
  const response = await ProtectedAPI.delete(`/quotes/${id}`);
  return response.data;
};

export const updateQuote = async (
  id: string,
  payload: { quote: string; author: string }
) => {
  const response = await ProtectedAPI.put(`/quotes/${id}`, payload);
  return response.data;
};

export const getQuotes = async () => {
  const response = await ProtectedAPI.get(`/quotes`);
  return response.data;
};

export const addGoals = async (payload: { title: string; description: string }) => {
  const response = await ProtectedAPI.post(`/goals`, payload);
  return response.data;
};

export const deleteGoal = async (id: string) => {
  const response = await ProtectedAPI.delete(`/goals/${id}`);
  return response.data;
};

export const updateGoal = async (
  id: string,
  payload: { title: string; description: string }
) => {
  const response = await ProtectedAPI.put(`/goals/${id}`, payload);
  return response.data;
};

export const getGoals = async () => {
  const response = await ProtectedAPI.get(`/goals`);
  return response.data;
};

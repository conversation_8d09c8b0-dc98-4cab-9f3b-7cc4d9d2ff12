import { ProtectedAPI } from "./axiosInstance";

const getImages = async () => {
  const response = await ProtectedAPI.get(`/image/getImages`);
  return response.data;
};

const uploadImage = async (file:any) => {
    const formData = new FormData();
    formData.append("file", file);

  const response = await ProtectedAPI.post(`/image/upload`,formData, {
    headers:{
        "Content-Type": "multipart/form-data", 
    }
  } );
  return response.data;
};

const deleteImage = async (url:string) => {
    const response = await ProtectedAPI.delete(`/image/delete?url=${url}`);
    return response.data;
  };

export { getImages, uploadImage,deleteImage };

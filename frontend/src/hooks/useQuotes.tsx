import { createContext, useContext } from "react";
import { QuoteContextType } from "../context/QuoteContext";

export const QuoteContext = createContext<QuoteContextType | undefined>(undefined);

export const useQuotes = (): QuoteContextType => {
    const context = useContext(QuoteContext);
    if (!context) {
      throw new Error("useQuotes must be used within a QuoteProvider");
    }
    return context;
  };
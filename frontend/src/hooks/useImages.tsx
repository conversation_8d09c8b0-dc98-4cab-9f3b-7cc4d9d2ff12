import { createContext, useContext } from "react";
import { ImageContextType } from "../context/ImageContext";

export const ImageContext = createContext<ImageContextType | undefined>(
  undefined
);

export const useImages = (): ImageContextType => {
  const context = useContext(ImageContext);
  if (!context) {
    throw new Error("useImages must be used within an ImageProvider");
  }
  return context;
};

import { useState, useLayoutEffect } from "react";

type Props = {
  screenHeight: number;
  screenWidth: number;
};

const useWindowResize = () => {
  const [size, setSize] = useState<Props>({
    screenWidth: window.innerWidth,
    screenHeight: window.innerHeight,
  });

  useLayoutEffect(() => {
    const updateSize = () => {
      setSize({ screenWidth: window.innerWidth, screenHeight: window.innerHeight });
    };

    window.addEventListener("resize", updateSize);
    updateSize(); // Ensure initial size is set correctly

    return () => window.removeEventListener("resize", updateSize);
  }, []);

  return size;
};
export default useWindowResize;

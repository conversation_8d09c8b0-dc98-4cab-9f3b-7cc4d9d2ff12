import { useEffect, useRef, useState } from "react";
import printIcon from "../../assets/images/PrinterIcon.svg";
import flowerIcon from "../../assets/images/flowerIcon.svg";
import arrowIcon from "../../assets/images/arrowIcon.svg";
import { useNavigate, useParams } from "react-router-dom";
import { FadeLoader } from "react-spinners";
import { AnimatePresence, motion } from "framer-motion";
import TextEditor from "../common/TextEditor";
import { EditOutlined } from "@ant-design/icons";
import { useReactToPrint } from "react-to-print";
import EmailSection from "../dashboard/emailSection/EmailSection";
import { generateReport } from "../../services/reportService";
import { useUser } from "../../hooks/useUser";

const ReportSection = () => {
  const [loader, setLoader] = useState(true);
  const { user } = useUser();
  const { chatId } = useParams();
  const [openEmailSection, setOpenEmailSection] = useState(false);
  const [, setShowOptions] = useState(false);
  const [isOpenMenu, setisOpenMenu] = useState(false);
  const [activeIndex, setActiveIndex] = useState("introduction");
  const [activeEdit, setActiveEdit] = useState<string | null>(null);
  const navigate = useNavigate();
  const [sections, setSections] = useState<any>([]);
  const [sectionheader, setSectionHeader] = useState<any>({});

  const printRef = useRef<HTMLDivElement>(null);

  const handlePrint = useReactToPrint({
    contentRef: printRef,
  });

  const sendEmail = () => {
    const to = user?.email;
    const subject = encodeURIComponent("Hello from React!");
    const body = encodeURIComponent(`testing body`);

    const mailtoLink = `mailto:${to}?subject=${subject}&body=${body}`;
    window.location.href = mailtoLink;
  };

  const sectionRefs: Record<string, React.RefObject<HTMLElement | null>> = {
    introduction: useRef(null),
    technology: useRef(null),
    healthcare: useRef(null),
    finance: useRef(null),
    appendix: useRef(null),
  };

  const scrollToSection = (id: string) => {
    setActiveIndex(id);
    sectionRefs[id].current?.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
  };

  const GenerateReport = async () => {
    try {
      const conversationData = await generateReport(chatId);
      setSections(conversationData.data.sections);
      setSectionHeader({
        title: conversationData.data.title,
        subTitle: conversationData.data.subtitle,
      });
      setLoader(false);
    } catch (error) {
      console.error("Error:", error);
      setLoader(false);
    }
  };

  useEffect(() => {
    GenerateReport();
  }, [chatId]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveIndex(entry.target.id);
          }
        });
      },
      { threshold: 0.6 }
    );

    Object.values(sectionRefs).forEach((ref) => {
      if (ref.current) observer.observe(ref.current);
    });

    return () => {
      Object.values(sectionRefs).forEach((ref) => {
        if (ref.current) observer.unobserve(ref.current);
      });
    };
  }, []);
  return (
    <>
      <AnimatePresence mode="wait">
        {loader ? (
          <>
            <motion.div
              key="loader"
              initial={{ opacity: 0, y: 0 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -200 }}
              transition={{
                duration: 0.5,
                ease: "easeInOut",
              }}
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                height: "calc(100vh - 138px)",
                gap: "18px",
              }}
            >
              <FadeLoader />
              <h1 className="  font-semibold text-[22px] leading-[100%] tracking-[0%] text-center">
                Generating Report...
              </h1>
              <h6 className="  font-normal text-[18px] leading-[100%] tracking-[0%] text-center">
                Please Wait
              </h6>
            </motion.div>
          </>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 0 }}
            transition={{
              duration: 0.5,
              ease: "easeInOut",
            }}
            key="report"
            className="py-20 flex px-6 sm:px-8 lg:px-0 report-section"
          >
            <aside className="w-[25%] lg:flex flex-col items-center gap-10 hidden">
              <button
                className="flex justify-center bg-[#1E1E1E] p-3 rounded-[50%] cursor-pointer"
                onClick={() => navigate(-1)}
              >
                <img
                  src={arrowIcon}
                  alt="icon"
                  height={17}
                  width={20}
                  className="rotate-180"
                />
              </button>
              <nav className="flex flex-col">
                {sections.map(({ section_name }: any) => (
                  <button
                    key={section_name}
                    className={`  ${
                      activeIndex === section_name
                        ? "font-black"
                        : "font-medium"
                    } text-[14px] leading-[32px] tracking-[0.01em] text-center cursor-pointer`}
                    onClick={() => scrollToSection(section_name)}
                  >
                    {section_name}
                  </button>
                ))}
              </nav>
            </aside>
            <main className="w-full lg:w-[75%] print-area" ref={printRef}>
              <div className="flex flex-col gap-3 w-full lg:w-[75%]">
                <h1 className="  font-extrabold text-[34px] sm:text-[55px] leading-[40px] sm:leading-[60px] tracking-[0.01em]">
                  {/* Exciting Job Opportunities Near You */}
                  {sectionheader.title}
                </h1>
                <div className="flex justify-between flex-col gap-4 md:flex-row">
                  <h2 className="  font-semibold text-[18px] sm:text-[24px] leading-[24px] tracking-[0.01em] text-[#606060]">
                    {/* Explore Growing Careers in Your Area */}
                    {sectionheader.subTitle}
                  </h2>
                  <div className="flex gap-2.5 no-print">
                    <img
                      src={printIcon}
                      alt="icon"
                      className="w-[24px] h-[24px] cursor-pointer"
                      onClick={() => handlePrint()}
                    />
                    <div className="relative">
                      <motion.img
                        animate={{
                          rotate: isOpenMenu ? 225 : 0,
                          filter: isOpenMenu
                            ? "brightness(0) saturate(100%)"
                            : "brightness(1) saturate(100%)",
                        }}
                        transition={{ duration: 0.5, ease: "easeInOut" }}
                        src={flowerIcon}
                        alt="icon"
                        className="w-[24px] h-[24px] cursor-pointer"
                        onClick={() => {
                          setisOpenMenu(!isOpenMenu);
                          setShowOptions(false);
                        }}
                      />
                      <AnimatePresence>
                        {isOpenMenu && (
                          <>
                            <motion.div
                              initial={{ opacity: 0, y: 0 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: 0 }}
                              transition={{ duration: 0.5, ease: "easeInOut" }}
                              className="absolute left-8 bottom-8  items-center z-50 flex flex-col gap-2 w-max">
                              <button
                                className="  font-bold text-[17px] leading-[100%] tracking-[0%] w-fit px-4 py-2.5 text-center text-white bg-[#1E1E1E] rounded-[25px] cursor-pointer"
                                onClick={sendEmail}
                              >
                                Generate Email
                              </button>
                            </motion.div>
                          </>
                        )}
                      </AnimatePresence>
                    </div>
                  </div>
                </div>
              </div>

              {sections.map(
                ({ section_name, section_content }: any, index: number) => (
                  <section
                    key={section_name}
                    ref={sectionRefs[section_name]}
                    id={section_name}
                    className="w-full lg:w-[75%] mt-14 font-['Charter']"
                  >
                    <h1
                      className={`  font-bold text-[20px] leading-[32px] tracking-[0.01em] text-start cursor-pointer mb-2 font-['Inter']`}
                    >
                      {section_name}{" "}
                      <span
                        onClick={() => setActiveEdit(section_name)}
                        className="no-print"
                      >
                        <EditOutlined />
                      </span>
                    </h1>
                    {activeEdit !== section_name ? (
                      <>
                        <div
                          dangerouslySetInnerHTML={{ __html: section_content }}
                        />
                      </>
                    ) : (
                      <TextEditor
                        description={section_content}
                        id={index}
                        setSections={setSections}
                        setActiveEdit={setActiveEdit}
                      />
                    )}
                  </section>
                )
              )}
            </main>
          </motion.div>
        )}
      </AnimatePresence>
      <AnimatePresence>
        {openEmailSection && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-white/50 backdrop-blur-sm z-[499]"
              onClick={() => setOpenEmailSection(false)}
            />
            <EmailSection setOpenEmailSection={setOpenEmailSection} />
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default ReportSection;

import { render, screen, fireEvent } from "@testing-library/react";
import SearchBar from "../common/SearchBar";
import { describe, it, expect, vi } from "vitest";

describe("SearchBar component", () => {
  const setup = (propsOverrides = {}) => {
    const defaultProps = {
      color: "#000000",
      border: true,
      inputValue: "",
      placeholder: "Search something...",
      setInputValue: vi.fn(),
      handleSearch: vi.fn(),
      disable: true,
      resize: false
    };

    const props = { ...defaultProps, ...propsOverrides };

    render(<SearchBar {...props} />);
    return props;
  };

  it("renders input and button", () => {
    setup();
    expect(screen.getByPlaceholderText("Search something...")).toBeInTheDocument();
    expect(screen.getByRole("button")).toBeInTheDocument();
  });

  it("calls setInputValue on input change", () => {
    const setInputValue = vi.fn();
    setup({ setInputValue });

    const input = screen.getByPlaceholderText("Search something...");
    fireEvent.change(input, { target: { value: "test" } });

    expect(setInputValue).toHaveBeenCalledWith("test");
  });

  it("disables the button when input is empty", () => {
    setup({ inputValue: "" });
    const button = screen.getByRole("button");
    expect(button).toBeDisabled();
  });

  it("enables the button when input has value", () => {
    setup({ inputValue: "hello" });
    const button = screen.getByRole("button");
    expect(button).not.toBeDisabled();
  });

  it("calls handleSearch when button is clicked", () => {
    const handleSearch = vi.fn();
    setup({ inputValue: "search me", handleSearch });

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(handleSearch).toHaveBeenCalled();
  });
});

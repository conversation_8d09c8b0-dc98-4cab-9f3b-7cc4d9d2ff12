import { useEffect, useState } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

type Props = {
  description: string;
  id: number;
  setSections: (value: object) => void;
  setActiveEdit: (value: string | null) => void;
};

const TextEditor = ({ description, id, setSections, setActiveEdit }: Props) => {
  const [content, setContent] = useState("");
  const handleChange = (value: string) => {
    setContent(value);
  };

  const handleSave = () => {
    setSections((prevSections: []) =>
      prevSections.map((section: object, index: number) =>
        index === id ? { ...section, section_content: content } : section
      )
    );
    setActiveEdit(null);
  };
  useEffect(() => {
    setContent(description);
  }, [description]);
  return (
    <>
      <div>
        <ReactQuill value={content} onChange={handleChange} theme="snow"  />
        <button
          onClick={handleSave}
          style={{ marginTop: "10px", padding: "8px" }}
          className="cursor-pointer"
        >
          Save
        </button>
        <button
          onClick={() => setActiveEdit(null)}
          style={{ marginTop: "10px", padding: "8px" }}
          className="cursor-pointer"
        >
          Cancel
        </button>
      </div>
    </>
  );
};

export default TextEditor;

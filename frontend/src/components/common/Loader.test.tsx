import { render, screen } from "@testing-library/react";
import Loader from "../common/Loader";
import { describe, it, expect } from "vitest";

describe("Loader Component", () => {
  it("should not render anything when loading is false", () => {
    const { container } = render(<Loader loading={false} />);
    expect(container.firstChild).toBeNull();
  });

  it("should render the loader container when loading is true", () => {
    render(<Loader loading={true} />);
    const loaderContainer = screen.getByTestId("loader-container");
    expect(loaderContainer).toBeInTheDocument();
  });
});

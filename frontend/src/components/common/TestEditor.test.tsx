import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import TextEditor from "../common/TextEditor";
import { describe, it, expect, vi } from "vitest";

vi.mock("react-quill", () => ({
  __esModule: true,
  default: ({ value, onChange }: any) => (
    <textarea
      data-testid="quill-editor"
      value={value}
      onChange={(e) => onChange(e.target.value)}
    />
  ),
}));

describe("TextEditor", () => {
  const mockSetSections = vi.fn();
  const mockSetActiveEdit = vi.fn();

  const defaultProps = {
    description: "<p>Initial content</p>",
    id: 0,
    setSections: mockSetSections,
    setActiveEdit: mockSetActiveEdit,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders editor with initial description", () => {
    render(<TextEditor {...defaultProps} />);
    const editor = screen.getByTestId("quill-editor");
    expect(editor).toBeInTheDocument();
    expect(editor).toHaveValue("<p>Initial content</p>");
  });

  it("updates content on typing", () => {
    render(<TextEditor {...defaultProps} />);
    const editor = screen.getByTestId("quill-editor");
    fireEvent.change(editor, { target: { value: "<p>Updated content</p>" } });
    expect(editor).toHaveValue("<p>Updated content</p>");
  });

  it("calls setSections and setActiveEdit on save", async () => {
    const customSetSections = vi.fn((cb) =>
      cb([{ description: "Old" }])
    );

    render(
      <TextEditor
        {...defaultProps}
        setSections={customSetSections}
      />
    );

    const editor = screen.getByTestId("quill-editor");
    fireEvent.change(editor, { target: { value: "<p>New content</p>" } });

    const saveButton = screen.getByText("Save");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(customSetSections).toHaveBeenCalled();
      expect(mockSetActiveEdit).toHaveBeenCalledWith(null);
    });
  });

  it("calls setActiveEdit with null on cancel", () => {
    render(<TextEditor {...defaultProps} />);
    const cancelButton = screen.getByText("Cancel");
    fireEvent.click(cancelButton);
    expect(mockSetActiveEdit).toHaveBeenCalledWith(null);
  });
});

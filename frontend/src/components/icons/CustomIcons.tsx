type IconProps = {
  width?: string;
  height?: string;
  color?: string;
};

export const AudioIcon = ({
  width = "24",
  height = "24",
  color = "currentColor",
}: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.5 4C8.67157 4 8 4.67157 8 5.5V18.5C8 19.3284 8.67157 20 9.5 20C10.3284 20 11 19.3284 11 18.5V5.5C11 4.67157 10.3284 4 9.5 4Z"
      fill={color}
    ></path>
    <path
      d="M13 8.5C13 7.67157 13.6716 7 14.5 7C15.3284 7 16 7.67157 16 8.5V15.5C16 16.3284 15.3284 17 14.5 17C13.6716 17 13 16.3284 13 15.5V8.5Z"
      fill={color}
    ></path>
    <path
      d="M4.5 9C3.67157 9 3 9.67157 3 10.5V13.5C3 14.3284 3.67157 15 4.5 15C5.32843 15 6 14.3284 6 13.5V10.5C6 9.67157 5.32843 9 4.5 9Z"
      fill={color}
    ></path>
    <path
      d="M19.5 9C18.6716 9 18 9.67157 18 10.5V13.5C18 14.3284 18.6716 15 19.5 15C20.3284 15 21 14.3284 21 13.5V10.5C21 9.67157 20.3284 9 19.5 9Z"
      fill={color}
    ></path>
  </svg>
);

export const ShareIcon = ({
  width = "24",
  height = "24",
  color = "currentColor",
}: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    aria-label="Upload Icon"
  >
    <path
      d="M6.66669 6.66671L10 3.33337L13.3334 6.66671M10 3.75004V12.5"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.33331 11.6666V11.8666C3.33331 13.5468 3.33331 14.3869 3.66029 15.0286C3.94791 15.5931 4.40686 16.052 4.97134 16.3396C5.61308 16.6666 6.45316 16.6666 8.13331 16.6666H11.8666C13.5468 16.6666 14.3869 16.6666 15.0286 16.3396C15.5931 16.052 16.052 15.5931 16.3397 15.0286C16.6666 14.3869 16.6666 13.5468 16.6666 11.8666V11.6666"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
    />
  </svg>
);

export const CameraIcon = ({
  width = "24",
  height = "24",
  color = "currentColor",
}: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 11 11"
    fill="none"
  >
    <path
      d="M10.5418 8.70833C10.5418 8.95145 10.4453 9.18461 10.2733 9.35651C10.1014 9.52842 9.86828 9.625 9.62516 9.625H1.37516C1.13205 9.625 0.89889 9.52842 0.726982 9.35651C0.555073 9.18461 0.458496 8.95145 0.458496 8.70833V3.66667C0.458496 3.42355 0.555073 3.19039 0.726982 3.01849C0.89889 2.84658 1.13205 2.75 1.37516 2.75H3.2085L4.12516 1.375H6.87516L7.79183 2.75H9.62516C9.86828 2.75 10.1014 2.84658 10.2733 3.01849C10.4453 3.19039 10.5418 3.42355 10.5418 3.66667V8.70833Z"
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.50016 7.79167C6.51269 7.79167 7.3335 6.97086 7.3335 5.95833C7.3335 4.94581 6.51269 4.125 5.50016 4.125C4.48764 4.125 3.66683 4.94581 3.66683 5.95833C3.66683 6.97086 4.48764 7.79167 5.50016 7.79167Z"
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);


  export const LinkIcon = ({
    width = "24",
    height = "24",
    color = "currentColor",
  }: IconProps) => (
    <svg
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    aria-label="link icon"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18.2929 5.70711C16.4743 3.88849 13.5257 3.88849 11.7071 5.7071L10.7071 6.70711C10.3166 7.09763 9.68341 7.09763 9.29289 6.70711C8.90236 6.31658 8.90236 5.68342 9.29289 5.29289L10.2929 4.29289C12.8926 1.69323 17.1074 1.69323 19.7071 4.29289C22.3068 6.89256 22.3068 11.1074 19.7071 13.7071L18.7071 14.7071C18.3166 15.0976 17.6834 15.0976 17.2929 14.7071C16.9024 14.3166 16.9024 13.6834 17.2929 13.2929L18.2929 12.2929C20.1115 10.4743 20.1115 7.52572 18.2929 5.70711ZM15.7071 8.29289C16.0976 8.68342 16.0976 9.31658 15.7071 9.70711L9.7071 15.7071C9.31658 16.0976 8.68341 16.0976 8.29289 15.7071C7.90236 15.3166 7.90236 14.6834 8.29289 14.2929L14.2929 8.29289C14.6834 7.90237 15.3166 7.90237 15.7071 8.29289ZM6.7071 9.29289C7.09763 9.68342 7.09763 10.3166 6.7071 10.7071L5.7071 11.7071C3.88849 13.5257 3.88849 16.4743 5.7071 18.2929C7.52572 20.1115 10.4743 20.1115 12.2929 18.2929L13.2929 17.2929C13.6834 16.9024 14.3166 16.9024 14.7071 17.2929C15.0976 17.6834 15.0976 18.3166 14.7071 18.7071L13.7071 19.7071C11.1074 22.3068 6.89255 22.3068 4.29289 19.7071C1.69322 17.1074 1.69322 12.8926 4.29289 10.2929L5.29289 9.29289C5.68341 8.90237 6.31658 8.90237 6.7071 9.29289Z"
      fill={color}
    />
  </svg>
  );
  
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import ResetPassword from "./ResetPassword";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import * as authService from "../../services/authService";
import { vi } from "vitest";

// Mock the resetPassword service
vi.mock("../../services/authService", () => ({
  resetPassword: vi.fn(),
}));

// Mock useNavigate and useParams
const mockedUsedNavigate = vi.fn();

vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual<any>("react-router-dom");
  return {
    ...actual,
    useNavigate: () => mockedUsedNavigate,
    useParams: () => ({ token: "mocked-token" }),
  };
});

const renderComponent = () => {
  render(
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<ResetPassword />} />
      </Routes>
    </BrowserRouter>
  );
};

describe("ResetPassword Component", () => {
  it("renders input and submit button", () => {
    renderComponent();
    expect(screen.getByPlaceholderText("Create New Password")).toBeInTheDocument();
    expect(screen.getByRole("button")).toBeInTheDocument();
  });

  it("submits form and navigates on success", async () => {
    (authService.resetPassword as any).mockResolvedValueOnce({});
    renderComponent();

    const input = screen.getByPlaceholderText("Create New Password");
    fireEvent.change(input, { target: { value: "MyNewSecurePass123" } });

    const submitButton = screen.getByRole("button");
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(authService.resetPassword).toHaveBeenCalledWith({
        currentPassword: undefined,
        newPassword: "MyNewSecurePass123",
        token: "mocked-token",
      });
      expect(mockedUsedNavigate).toHaveBeenCalledWith("/search");
    });
  });

  it("shows error alert on failed submit", async () => {
    (authService.resetPassword as any).mockRejectedValueOnce({
      response: {
        data: { message: "Something went wrong!" },
      },
    });

    renderComponent();

    const input = screen.getByPlaceholderText("Create New Password");
    fireEvent.change(input, { target: { value: "FailingPassword123" } });

    const submitButton = screen.getByRole("button");
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText("Something went wrong!")).toBeInTheDocument();
    });
  });
});

import headingImg from "../../assets/images/headingImg.svg";
import { EyeInvisibleOutlined, EyeOutlined } from "@ant-design/icons";
import { Alert, Form } from "antd";
import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import arrowIcon from "../../assets/images/arrowIcon.svg";
import { resetPassword } from "../../services/authService";
import Loader from "../common/Loader";
import { useUser } from "../../hooks/useUser";
import maskGroup from "../../assets/images/Maskgroup.png";
import imgText from "../../assets/images/textImg.svg";

const ResetPassword = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const { token } = useParams();
  const { loginUserDetails } = useUser();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);

  const toggleConfirmPasswordVisibility = () => {
    setConfirmPasswordVisible((prev) => !prev);
  };
  const onFinish = ({
    currentPassword,
    newPassword,
  }: {
    currentPassword: string;
    newPassword: string;
  }) => {
    setIsLoading(true);
    resetPassword({ currentPassword, newPassword, token })
      .then(
        (res: {
          data: {
            user: {
              name: string;
              email: string;
              token: string;
              firstName: string;
              lastName: string;
              username: string;
            };
          };
        }) => {
          loginUserDetails(res?.data?.user);
          // navigate("/search");
          const token: any = localStorage.getItem("authToken");
          window.open(
            `https://deep.mergenai.io/chat?token=${encodeURIComponent(token)}`,
            "_self"
          );
        }
      )
      .catch((error) => {
        form.resetFields();
        setAlertMessage(error?.response?.data?.message);
        console.error("User fetch error:", error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };
  return (
    <>
      {/* <div className="flex flex-col justify-center items-center h-[100vh] mx-8">
        <div>
          {alertMessage && (
            <>
              <Alert
                message={alertMessage}
                type="error"
                showIcon
                closable
                className="mb-5"
                style={{ marginBottom: "30px" }}
                onClose={() => setAlertMessage("")}
              />
            </>
          )}
          <img
            src={headingImg}
            alt="headingImg"
            loading="eager"
            className="mix-blend-screen w-[460px] h-[120px] invert"
          />
          <div className="flex flex-col gap-4 my-5">
            <Form
              name="login"
              initialValues={{ remember: true }}
              onFinish={onFinish}
              className="w-full"
            >
              <Form.Item
                name="newPassword"
                rules={[
                  { required: true, message: "Please confirm your Password!" },
                ]}
              >
                <Input
                  prefix={<LockOutlined className="me-2" />}
                  type={confirmPasswordVisible ? "text" : "password"}
                  placeholder="Create New Password"
                  className="w-full px-5 py-3 rounded-lg border border-gray-700 bg-[#111111] text-white placeholder:text-gray-500 font-medium tracking-wide shadow-[0_0_10px_rgba(0,0,0,0.3)] focus:ring-2 focus:ring-gray-600 focus:outline-none transition duration-300"
                  suffix={
                    confirmPasswordVisible ? (
                      <EyeInvisibleOutlined
                        onClick={toggleConfirmPasswordVisibility}
                      />
                    ) : (
                      <EyeOutlined onClick={toggleConfirmPasswordVisibility} />
                    )
                  }
                />
              </Form.Item>

              <Form.Item className="flex justify-center">
                <button
                  className={`bg-[#1E1E1E] py-3 px-6 rounded-[25px] cursor-pointer`}
                >
                  <span className="text-white flex gap-3">
                    Update Password
                    <img src={arrowIcon} alt="icon" height={12} width={14} />
                  </span>
                </button>
              </Form.Item>
            </Form>
          </div>
        </div>
        <Loader loading={isLoading} />
      </div> */}

      <div className="flex lg:px-11 px-6 py-9 items-start font-Inter">
        <div className="relative w-[52%] hidden md:block">
          <img
            src={maskGroup}
            alt="bgImg"
            loading="eager"
            className="w-full h-[calc(100vh-80px)]"
          />
          <img
            src={headingImg}
            alt="headingImg"
            loading="eager"
            className="invert max-h-[40px] max-w-[159px] absolute top-[50px] left-[37px]"
          />
          <img
            src={imgText}
            alt="headingImg"
            loading="eager"
            className="absolute bottom-[50px] left-[37px] lg:w-auto w-[80%]"
          />
        </div>
        <div className="md:w-[48%] w-full lg:px-[71px] md:px-8 px-2">
          <div className="pt-16">
            <h1 className="font-Inter font-bold md:text-[45px] leading-[100%] tracking-[0%] text-[32px]">
              Update Password!
            </h1>

            <div className="flex relative mt-8 mb-14">
              <button
                className={`bg-[#1E1E1E] py-4 px-6 rounded-[42px] cursor-pointer w-[50%] z-2`}
              >
                <span className="text-white flex gap-3 font-extrabold text-[16px] justify-center">
                  Log In
                </span>
              </button>
              <button
                className={`bg-[#8C8C8C] py-4 px-6 rounded-[42px] cursor-pointer w-full absolute top-0 flex justify-end`}
                onClick={() => navigate("/signUp")}
              >
                <span className="text-white flex gap-3 font-extrabold text-[16px] w-[50%] justify-center">
                  Sign Up
                </span>
              </button>
            </div>

            {alertMessage && (
              <>
                <Alert
                  message={alertMessage}
                  type={"error"}
                  showIcon
                  closable
                  className="mb-5"
                  style={{ marginBottom: "30px" }}
                  onClose={() => {
                    setAlertMessage("");
                  }}
                />
              </>
            )}
            <Form
              name="login"
              form={form}
              initialValues={{ remember: true }}
              onFinish={onFinish}
              className="w-full flex flex-col items-start gap-2"
            >
              <div className="relative w-full">
                <Form.Item
                  name="newPassword"
                  rules={[
                    { required: true, message: "Please input your Password!" },
                  ]}
                  className="w-full"
                >
                  <input
                    value={form.getFieldValue("newPassword")}
                    onChange={(e) =>
                      form.setFieldsValue({ newPassword: e.target.value })
                    }
                    placeholder="Create New Password"
                    className="bg-[#1D1D1DCC] shadow-[inset_0px_4px_4px_0px_#00000040] rounded-[42px] font-[Inter] font-normal text-[14px] leading-[100%] tracking-[0%] py-3.5 text-white ps-7 outline-0 w-full"
                    type={confirmPasswordVisible ? "text" : "password"}
                  />
                </Form.Item>
                <span className="absolute right-5 top-6 -translate-y-1/2 text-white cursor-pointer">
                  {confirmPasswordVisible ? (
                    <EyeInvisibleOutlined
                      onClick={toggleConfirmPasswordVisibility}
                      className="text-gray-400 cursor-pointer hover:text-white transition"
                    />
                  ) : (
                    <EyeOutlined
                      onClick={toggleConfirmPasswordVisibility}
                      className="text-gray-400 cursor-pointer hover:text-white transition"
                    />
                  )}
                </span>
              </div>

              <Form.Item className="flex justify-center">
                <button
                  className={`bg-[#1E1E1E] py-3 px-6 rounded-[25px] cursor-pointer mt-5`}
                >
                  <span className="text-white flex gap-3 font-extrabold text-[18px]">
                    Update Password
                    <img src={arrowIcon} alt="icon" height={12} width={14} />
                  </span>
                </button>
              </Form.Item>
            </Form>
            <Loader loading={isLoading} />
          </div>
        </div>
      </div>
    </>
  );
};

export default ResetPassword;

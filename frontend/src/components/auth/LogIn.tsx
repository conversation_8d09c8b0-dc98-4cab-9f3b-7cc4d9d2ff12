import headingImg from "../../assets/images/headingImg.svg";
import { EyeInvisibleOutlined, EyeOutlined } from "@ant-design/icons";
import { Form, Alert } from "antd";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { login } from "../../services/authService";
import arrowIcon from "../../assets/images/arrowIcon.svg";
import Loader from "../common/Loader";
import { useUser } from "../../hooks/useUser";
import maskGroup from "../../assets/images/Maskgroup.png";
import imgText from "../../assets/images/textImg.svg";

const LogIn = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  const { loginUserDetails } = useUser();
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const togglePasswordVisibility = () => {
    setPasswordVisible((prev) => !prev);
  };
  const onFinish = ({
    username,
    password,
  }: {
    username: string;
    password: string;
  }) => {
    setIsLoading(true);
    login({ username, password })
      .then(
        (res: {
          user: {
            name: string;
            email: string;
            token: string;
            firstName: string;
            lastName: string;
            username: string;
          };
          message: string;
        }) => {
          if (res.message) {
            setSuccessMessage(res.message);
            setAlertMessage("");
            form.resetFields();
            return;
          }
          loginUserDetails(res.user);
          // navigate("/search");
          const token: any = localStorage.getItem("authToken");
          window.open(
            `https://deep.mergenai.io/chat?token=${encodeURIComponent(token)}`,
            "_self"
          );
        }
      )
      .catch((error) => {
        form.resetFields();
        setAlertMessage(error?.response?.data?.message);
        setSuccessMessage("");
        console.error("User fetch error:", error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };
  useEffect(() => {
    if (location?.state?.showMessage) {
      setSuccessMessage(location.state.showMessage);
    }
  }, [location?.state?.showMessage]);
  return (
    <>
      <div className="flex lg:px-11 px-6 py-9 items-start font-Inter">
        <div className="relative w-[52%] hidden md:block">
          <img
            src={maskGroup}
            alt="bgImg"
            loading="eager"
            className="w-full h-[calc(100vh-80px)]"
          />
          <img
            src={headingImg}
            alt="headingImg"
            loading="eager"
            className="invert max-h-[40px] max-w-[159px] absolute top-[50px] left-[37px]"
          />
          <img
            src={imgText}
            alt="headingImg"
            loading="eager"
            className="absolute bottom-[50px] left-[37px] lg:w-auto w-[80%]"
          />
        </div>
        <div className="md:w-[48%] w-full lg:px-[71px] md:px-8 px-2">
          <div className="pt-16">
            <h1 className="font-Inter font-bold md:text-[45px] leading-[100%] tracking-[0%] text-[32px]">
              Welcome back!
            </h1>

            <div className="flex relative mt-8 mb-14">
              <button
                className={`bg-[#1E1E1E] py-4 px-6 rounded-[42px] cursor-pointer w-[50%] z-2`}
              >
                <span className="text-white flex gap-3 font-extrabold text-[16px] justify-center">
                  Log In
                </span>
              </button>
              <button
                className={`bg-[#8C8C8C] py-4 px-6 rounded-[42px] cursor-pointer w-full absolute top-0 flex justify-end`}
                onClick={() => navigate("/signUp")}
              >
                <span className="text-white flex gap-3 font-extrabold text-[16px] w-[50%] justify-center">
                  Sign Up
                </span>
              </button>
            </div>

            {(alertMessage || successMessage) && (
              <>
                <Alert
                  message={successMessage ? successMessage : alertMessage}
                  type={successMessage ? "success" : "error"}
                  showIcon
                  closable
                  className="mb-5"
                  style={{ marginBottom: "30px" }}
                  onClose={() => {
                    setAlertMessage("");
                    setSuccessMessage("");
                  }}
                />
              </>
            )}
            <Form
              name="login"
              form={form}
              initialValues={{ remember: true }}
              onFinish={onFinish}
              className="w-full flex flex-col items-start gap-2"
            >
              <Form.Item
                name="username"
                rules={[
                  { required: true, message: "Please input your Username!" },
                ]}
                className="w-full"
              >
                <input
                  value={form.getFieldValue("username")}
                  onChange={(e) =>
                    form.setFieldsValue({ username: e.target.value })
                  }
                  placeholder="Username"
                  className="bg-[#1D1D1DCC] shadow-[inset_0px_4px_4px_0px_#00000040] rounded-[42px] font-[Inter] font-normal text-[14px] leading-[100%] tracking-[0%] py-3.5 text-white ps-7 outline-0 w-full"
                />
              </Form.Item>

              <div className="relative w-full">
                <Form.Item
                  name="password"
                  rules={[
                    { required: true, message: "Please input your Password!" },
                  ]}
                  className="w-full"
                >
                  <input
                    value={form.getFieldValue("password")}
                    onChange={(e) =>
                      form.setFieldsValue({ password: e.target.value })
                    }
                    placeholder="Password"
                    className="bg-[#1D1D1DCC] shadow-[inset_0px_4px_4px_0px_#00000040] rounded-[42px] font-[Inter] font-normal text-[14px] leading-[100%] tracking-[0%] py-3.5 text-white ps-7 outline-0 w-full"
                    type={passwordVisible ? "text" : "password"}
                  />
                </Form.Item>

                <span className="absolute right-5 top-6 -translate-y-1/2 text-white cursor-pointer">
                  {passwordVisible ? (
                    <EyeInvisibleOutlined
                      onClick={togglePasswordVisibility}
                      className="text-gray-400 cursor-pointer hover:text-white transition"
                    />
                  ) : (
                    <EyeOutlined
                      onClick={togglePasswordVisibility}
                      className="text-gray-400 cursor-pointer hover:text-white transition"
                    />
                  )}
                </span>
              </div>

              <div className="flex justify-between w-full">
                <Form.Item className="flex justify-center">
                  <button
                    className={`bg-[#1E1E1E] py-3 px-6 rounded-[25px] cursor-pointer mt-5`}
                  >
                    <span className="text-white flex gap-3 font-extrabold text-[18px]">
                      Log In
                      <img src={arrowIcon} alt="icon" height={12} width={14} />
                    </span>
                  </button>
                </Form.Item>
                <button
                  className="text-[14px] font-bold cursor-pointer"
                  onClick={() => navigate("/forgetPassword")}
                >
                  Forgot password
                </button>
              </div>
            </Form>
            <Loader loading={isLoading} />
          </div>
        </div>
      </div>
    </>
  );
};

export default LogIn;

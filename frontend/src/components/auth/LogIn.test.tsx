import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { <PERSON>rowserRouter } from 'react-router-dom'
import LogIn from './LogIn'
import * as authService from '../../services/authService'

vi.mock('../../services/authService', () => ({
  login: vi.fn()
}))

const mockNavigate = vi.fn()

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual<typeof import('react-router-dom')>('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate
  }
})

const renderWithRouter = () =>
  render(
    <BrowserRouter>
      <LogIn />
    </BrowserRouter>
  )

describe('LogIn Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders form inputs and login button', () => {
    renderWithRouter()
    expect(screen.getByPlaceholderText(/username/i)).toBeInTheDocument()
    expect(screen.getByPlaceholderText(/password/i)).toBeInTheDocument()
    expect(screen.getByText(/log in/i)).toBeInTheDocument()
  })

  it('submits the form and navigates on success', async () => {
    const mockLogin = vi.spyOn(authService, 'login').mockResolvedValue({})

    renderWithRouter()

    fireEvent.change(screen.getByPlaceholderText(/username/i), {
      target: { value: 'testuser' }
    })
    fireEvent.change(screen.getByPlaceholderText(/password/i), {
      target: { value: 'password123' }
    })
    fireEvent.click(screen.getByText(/log in/i))

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        username: 'testuser',
        password: 'password123'
      })
      expect(mockNavigate).toHaveBeenCalledWith('/search')
    })
  })

  it('shows error message on failed login', async () => {
    const mockLogin = vi.spyOn(authService, 'login').mockRejectedValue({
      response: {
        data: { message: 'Invalid credentials' }
      }
    })

    renderWithRouter()

    fireEvent.change(screen.getByPlaceholderText(/username/i), {
      target: { value: 'wronguser' }
    })
    fireEvent.change(screen.getByPlaceholderText(/password/i), {
      target: { value: 'wrongpass' }
    })
    fireEvent.click(screen.getByText(/log in/i))

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalled()
      expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument()
    })
  })
})

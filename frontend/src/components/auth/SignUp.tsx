import headingImg from "../../assets/images/headingImg.svg";
import {
  EyeInvisibleOutlined,
  EyeOutlined
} from "@ant-design/icons";
import { Form, Alert } from "antd";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { register } from "../../services/authService";
import arrowIcon from "../../assets/images/arrowIcon.svg";
import Loader from "../common/Loader";
import { useUser } from "../../hooks/useUser";
import maskGroup from "../../assets/images/Maskgroup.png";
import imgText from "../../assets/images/textImg.svg";

const SignUp = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { loginUserDetails } = useUser();
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const togglePasswordVisibility = () => {
    setPasswordVisible((prev) => !prev);
  };
  const onFinish = ({
    username,
    password,
    email,
    firstName,
    lastName,
  }: {
    username: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
  }) => {
    setIsLoading(true);
    register({ username, password, email, firstName, lastName })
      .then(
        (res: {
          data: {
            user: {
              name: string;
              email: string;
              token: string;
              firstName: string;
              lastName: string;
              username: string;
            };
          };
          message: string;
        }) => {
          loginUserDetails(res?.data?.user);
          setSuccessMessage(res?.message);
          setAlertMessage("");
          form.resetFields();
          navigate("/", {
            state: {
              showMessage: res?.message,
            },
          });
          // const token:any = localStorage.getItem("authToken");
          // window.open(
          //   `https://deep.mergenai.io/chat?token=${encodeURIComponent(token)}`,
          //   "_self"
          // );
        }
      )
      .catch((error) => {
        setAlertMessage(error?.response?.data?.message);
        form.resetFields();
        setSuccessMessage("");
        console.error("User fetch error:", error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };
  return (
    <>
      {/* <div className="flex flex-col justify-center items-center h-[100vh] mx-8">
        <div>
          {(alertMessage || successMessage) && (
            <>
              <Alert
                message={successMessage ? successMessage : alertMessage}
                type={successMessage ? "success" : "error"}
                showIcon
                closable
                className="mb-5"
                style={{ marginBottom: "30px" }}
                onClose={() => {
                  setAlertMessage("");
                  setSuccessMessage("");
                }}
              />
            </>
          )}
          <img
            src={headingImg}
            alt="headingImg"
            loading="eager"
            className="mix-blend-screen w-[460px] h-[120px] invert"
          />
          <div className="flex flex-col gap-4 my-5">
            <Form
              form={form}
              name="login"
              initialValues={{ remember: true }}
              onFinish={onFinish}
              className="w-full"
            >
              <Form.Item
                name="firstName"
                rules={[
                  { required: true, message: "Please input your First Name!" },
                ]}
              >
                <Input
                  prefix={<FileTextOutlined className="me-2" />}
                  placeholder="First Name"
                  className="w-full px-5 py-3 rounded-lg border border-gray-700 bg-[#111111] text-white placeholder:text-gray-500 font-medium tracking-wide shadow-[0_0_10px_rgba(0,0,0,0.3)] focus:ring-2 focus:ring-gray-600 focus:outline-none transition duration-300"
                />
              </Form.Item>
              <Form.Item
                name="lastName"
                rules={[
                  { required: true, message: "Please input your Last Name!" },
                ]}
              >
                <Input
                  prefix={<FileTextOutlined className="me-2" />}
                  placeholder="Last Name"
                  className="w-full px-5 py-3 rounded-lg border border-gray-700 bg-[#111111] text-white placeholder:text-gray-500 font-medium tracking-wide shadow-[0_0_10px_rgba(0,0,0,0.3)] focus:ring-2 focus:ring-gray-600 focus:outline-none transition duration-300"
                />
              </Form.Item>
              <Form.Item
                name="username"
                rules={[
                  { required: true, message: "Please input your User Name!" },
                ]}
              >
                <Input
                  prefix={<UserOutlined className="me-2" />}
                  placeholder="User Name"
                  className="w-full px-5 py-3 rounded-lg border border-gray-700 bg-[#111111] text-white placeholder:text-gray-500 font-medium tracking-wide shadow-[0_0_10px_rgba(0,0,0,0.3)] focus:ring-2 focus:ring-gray-600 focus:outline-none transition duration-300"
                />
              </Form.Item>
              <Form.Item
                name="email"
                rules={[
                  { required: true, message: "Please input your Email!" },
                  { type: "email", message: "Please enter a valid Email!" },
                ]}
              >
                <Input
                  prefix={<MailOutlined className="me-2" />}
                  placeholder="Email"
                  className="w-full px-5 py-3 rounded-lg border border-gray-700 bg-[#111111] text-white placeholder:text-gray-500 font-medium tracking-wide shadow-[0_0_10px_rgba(0,0,0,0.3)] focus:ring-2 focus:ring-gray-600 focus:outline-none transition duration-300"
                />
              </Form.Item>
              <Form.Item
                name="password"
                rules={[
                  { required: true, message: "Please input your Password!" },
                ]}
              >
                <Input
                  prefix={<LockOutlined className="me-2" />}
                  type={passwordVisible ? "text" : "password"}
                  placeholder="Password"
                  className="w-full px-5 py-3 rounded-lg border border-gray-700 bg-[#111111] text-white placeholder:text-gray-500 font-medium tracking-wide shadow-[0_0_10px_rgba(0,0,0,0.3)] focus:ring-2 focus:ring-gray-600 focus:outline-none transition duration-300"
                  suffix={
                    passwordVisible ? (
                      <EyeInvisibleOutlined
                        onClick={togglePasswordVisibility}
                      />
                    ) : (
                      <EyeOutlined onClick={togglePasswordVisibility} />
                    )
                  }
                />
              </Form.Item>

              <Form.Item className="flex justify-center">
                <button
                  className={`bg-[#1E1E1E] py-3 px-6 rounded-[25px] cursor-pointer`}
                >
                  <span className="text-white flex gap-3">
                    Sign Up
                    <img src={arrowIcon} alt="icon" height={12} width={14} />
                  </span>
                </button>
              </Form.Item>
            </Form>
            <Form.Item>
              <Flex justify="center" align="center">
                <div>
                  Already have an account?{" "}
                  <button
                    className="text-[blue] cursor-pointer"
                    onClick={() => navigate("/")}
                  >
                    Log In
                  </button>
                </div>
              </Flex>
            </Form.Item>
          </div>
        </div>
        <Loader loading={isLoading} />
      </div> */}
      <div className="flex lg:px-11 px-6 py-9 items-start font-Inter">
        <div className="relative w-[52%] hidden md:block">
          <img
            src={maskGroup}
            alt="bgImg"
            loading="eager"
            className="w-full h-[calc(100vh-80px)]"
          />
          <img
            src={headingImg}
            alt="headingImg"
            loading="eager"
            className="invert max-h-[40px] max-w-[159px] absolute top-[50px] left-[37px]"
          />
          <img
            src={imgText}
            alt="headingImg"
            loading="eager"
            className="absolute bottom-[50px] left-[37px] lg:w-auto w-[80%]"
          />
        </div>
        <div className="md:w-[48%] w-full lg:px-[71px] md:px-8 px-2">
          <div className="pt-16">
            <h1 className="font-Inter font-bold md:text-[45px] leading-[100%] tracking-[0%] text-[32px]">
              Hello!
            </h1>

            <div className="flex relative mt-8 mb-14">
              <button
                className={` bg-[#8C8C8C] py-4 px-6 rounded-[42px] cursor-pointer w-[50%] z-2`}
                onClick={() => navigate("/")}
              >
                <span className="text-white flex gap-3 font-extrabold text-[16px] justify-center">
                  Log In
                </span>
              </button>
              <button
                className={`bg-[#1E1E1E] py-4 px-6 rounded-[42px] cursor-pointer w-full absolute top-0 flex justify-end`}
              >
                <span className="text-white flex gap-3 font-extrabold text-[16px] w-[50%] justify-center">
                  Sign Up
                </span>
              </button>
            </div>

            {(alertMessage || successMessage) && (
              <>
                <Alert
                  message={successMessage ? successMessage : alertMessage}
                  type={successMessage ? "success" : "error"}
                  showIcon
                  closable
                  className="mb-5"
                  style={{ marginBottom: "30px" }}
                  onClose={() => {
                    setAlertMessage("");
                    setSuccessMessage("");
                  }}
                />
              </>
            )}
            <Form
              name="login"
              form={form}
              initialValues={{ remember: true }}
              onFinish={onFinish}
              className="w-full flex flex-col items-start gap-2"
            >
              <div className="flex gap-9 w-full">
                <Form.Item
                  name="firstName"
                  rules={[
                    {
                      required: true,
                      message: "Please input your First Name!",
                    },
                  ]}
                  className="w-full"
                >
                  <input
                    value={form.getFieldValue("firstName")}
                    onChange={(e) =>
                      form.setFieldsValue({ firstName: e.target.value })
                    }
                    placeholder="First Name"
                    className="bg-[#1D1D1DCC] shadow-[inset_0px_4px_4px_0px_#00000040] rounded-[42px] font-[Inter] font-normal text-[14px] leading-[100%] tracking-[0%] py-3.5 text-white ps-7 outline-0 w-full"
                  />
                </Form.Item>
                <Form.Item
                  name="lastName"
                  rules={[
                    { required: true, message: "Please input your Last Name!" },
                  ]}
                  className="w-full"
                >
                  <input
                    value={form.getFieldValue("lastName")}
                    onChange={(e) =>
                      form.setFieldsValue({ lastName: e.target.value })
                    }
                    placeholder="Last Name"
                    className="bg-[#1D1D1DCC] shadow-[inset_0px_4px_4px_0px_#00000040] rounded-[42px] font-[Inter] font-normal text-[14px] leading-[100%] tracking-[0%] py-3.5 text-white ps-7 outline-0 w-full"
                  />
                </Form.Item>
              </div>

              <Form.Item
                name="email"
                rules={[
                  { required: true, message: "Please input your Email!" },
                  { type: "email", message: "Please enter a valid Email!" },
                ]}
                className="w-full"
              >
                <input
                  value={form.getFieldValue("email")}
                  onChange={(e) =>
                    form.setFieldsValue({ email: e.target.value })
                  }
                  placeholder="Email"
                  className="bg-[#1D1D1DCC] shadow-[inset_0px_4px_4px_0px_#00000040] rounded-[42px] font-[Inter] font-normal text-[14px] leading-[100%] tracking-[0%] py-3.5 text-white ps-7 outline-0 w-full"
                />
              </Form.Item>

              <Form.Item
                name="username"
                rules={[
                  { required: true, message: "Please input your Username!" },
                ]}
                className="w-full"
              >
                <input
                  value={form.getFieldValue("username")}
                  onChange={(e) =>
                    form.setFieldsValue({ username: e.target.value })
                  }
                  placeholder="Username"
                  className="bg-[#1D1D1DCC] shadow-[inset_0px_4px_4px_0px_#00000040] rounded-[42px] font-[Inter] font-normal text-[14px] leading-[100%] tracking-[0%] py-3.5 text-white ps-7 outline-0 w-full"
                />
              </Form.Item>

              {/* <Form.Item
                name="password"
                rules={[
                  { required: true, message: "Please input your Password!" },
                ]}
                className="relative w-full"
              >
                <input
                  value={form.getFieldValue("password")}
                  onChange={(e) =>
                    form.setFieldsValue({ password: e.target.value })
                  }
                  placeholder="Password"
                  className="bg-[#1D1D1DCC] shadow-[inset_0px_4px_4px_0px_#00000040] rounded-[42px] font-[Inter] font-normal text-[14px] leading-[100%] tracking-[0%] py-3.5 text-white ps-7 outline-0 w-full"
                  type={passwordVisible ? "text" : "password"}
                />
                <span className="absolute right-5 top-1/2 -translate-y-1/2 text-white cursor-pointer">
                  {passwordVisible ? (
                    <EyeInvisibleOutlined
                      onClick={togglePasswordVisibility}
                      className="text-gray-400 cursor-pointer hover:text-white transition"
                    />
                  ) : (
                    <EyeOutlined
                      onClick={togglePasswordVisibility}
                      className="text-gray-400 cursor-pointer hover:text-white transition"
                    />
                  )}
                </span>
              </Form.Item> */}

              <div className="relative w-full">
                <Form.Item
                  name="password"
                  rules={[
                    { required: true, message: "Please input your Password!" },
                  ]}
                  className="w-full"
                >
                  <input
                    value={form.getFieldValue("password")}
                    onChange={(e) =>
                      form.setFieldsValue({ password: e.target.value })
                    }
                    placeholder="Password"
                    className="bg-[#1D1D1DCC] shadow-[inset_0px_4px_4px_0px_#00000040] rounded-[42px] font-[Inter] font-normal text-[14px] leading-[100%] tracking-[0%] py-3.5 text-white ps-7 outline-0 w-full"
                    type={passwordVisible ? "text" : "password"}
                  />
                </Form.Item>

                <span className="absolute right-5 top-6 -translate-y-1/2 text-white cursor-pointer">
                  {passwordVisible ? (
                    <EyeInvisibleOutlined
                      onClick={togglePasswordVisibility}
                      className="text-gray-400 cursor-pointer hover:text-white transition"
                    />
                  ) : (
                    <EyeOutlined
                      onClick={togglePasswordVisibility}
                      className="text-gray-400 cursor-pointer hover:text-white transition"
                    />
                  )}
                </span>
              </div>
              <Form.Item className="flex justify-center">
                <button
                  className={`bg-[#1E1E1E] py-3 px-6 rounded-[25px] cursor-pointer mt-5`}
                >
                  <span className="text-white flex gap-3 font-extrabold text-[18px]">
                    Sign Up
                    <img src={arrowIcon} alt="icon" height={12} width={14} />
                  </span>
                </button>
              </Form.Item>
            </Form>
            <Loader loading={isLoading} />
          </div>
        </div>
      </div>
    </>
  );
};

export default SignUp;

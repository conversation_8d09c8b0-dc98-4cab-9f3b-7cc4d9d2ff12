// ForgetPassword.test.tsx
import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import ForgetPassword from "./ForgetPassword";
import { BrowserRouter } from "react-router-dom";

// Mock assets
vi.mock("../../assets/images/headingImg.webp", () => ({
  default: "headingImg.webp",
}));
vi.mock("../../assets/images/arrowIcon.svg", () => ({
  default: "arrowIcon.svg",
}));

// Mock Loader
vi.mock("../common/Loader", () => ({
  default: ({ loading }: { loading: boolean }) =>
    loading ? <div>Loading...</div> : null,
}));

// ✅ Define mock directly in vi.mock
vi.mock("../../services/authService", () => {
  return {
    forgetPassword: vi.fn(), // <-- define mock function here
  };
});

const renderWithRouter = () =>
  render(
    <BrowserRouter>
      <ForgetPassword />
    </BrowserRouter>
  );

describe("ForgetPassword Component", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders the form and elements correctly", () => {
    renderWithRouter();

    expect(screen.getByPlaceholderText("Email")).toBeInTheDocument();
    expect(screen.getByText("Continue")).toBeInTheDocument();
    expect(screen.getByText("Back to Login?")).toBeInTheDocument();
  });

  it("submits form and shows success message", async () => {
    const { forgetPassword }:any = await vi.importMock("../../services/authService");
    forgetPassword.mockResolvedValueOnce({});

    renderWithRouter();

    fireEvent.change(screen.getByPlaceholderText("Email"), {
      target: { value: "<EMAIL>" },
    });
    fireEvent.click(screen.getByText("Continue"));

    await waitFor(() => {
      expect(
        screen.getByText("Please check your email.")
      ).toBeInTheDocument();
    });
  });

  it("shows error message on API failure", async () => {
    const { forgetPassword }:any = await vi.importMock("../../services/authService");
    forgetPassword.mockRejectedValueOnce({
      response: { data: { message: "Email not found" } },
    });

    renderWithRouter();

    fireEvent.change(screen.getByPlaceholderText("Email"), {
      target: { value: "<EMAIL>" },
    });
    fireEvent.click(screen.getByText("Continue"));

    await waitFor(() => {
      expect(screen.getByText("Email not found")).toBeInTheDocument();
    });
  });
});

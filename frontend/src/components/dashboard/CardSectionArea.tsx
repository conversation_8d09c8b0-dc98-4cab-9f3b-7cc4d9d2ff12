import { <PERSON><PERSON>, Toolt<PERSON> } from "antd";
import { motion } from "framer-motion";
import MaskGroupIcon from "../../assets/images/MaskGroupIcon.svg";
import { useState } from "react";
const CardSectionArea = () => {
  const [selectedItem, setSelectedItem] = useState<null | [] | object>(null);
  const data = [
    {
      backgroundColor: "#F8DE7E", // Light Yellow
      name: "Name",
      yearToggle: ["2023", "2024"],
      topics: ["Topic", "Topic", "Topic", "Topic", "Topic", "Topic"],
    },
    {
      backgroundColor: "#E8AE77", // Peach
      name: "Name",
      yearToggle: ["2023", "2024"],
      topics: ["Topic", "Topic"],
    },
    {
      backgroundColor: "#D3D3D3", // Light Gray
      name: "Name",
      yearToggle: ["2023", "2024"],
      topics: ["Topic", "Topic", "Topic"],
    },
    {
      backgroundColor: "#FFFFCC", // Pale Yellow
      name: "Name",
      yearToggle: ["2023", "2024"],
      topics: ["Topic", "Topic"],
    },
    {
      backgroundColor: "#1E1E1E", // Black
      textColor: "#FFFFFF", // White Text
      name: "Name",
      yearToggle: ["2023", "2024"],
      topics: ["Topic", "Topic", "Topic", "Topic", "Topic"],
    },
    {
      backgroundColor: "#F8DE7E", // Light Yellow
      name: "Name",
      yearToggle: ["2023", "2024"],
      topics: ["Topic", "Topic", "Topic"],
    },
    {
      backgroundColor: "#F8DE7E", // Light Yellow
      name: "Name",
      yearToggle: ["2023", "2024"],
      topics: ["Topic", "Topic", "Topic", "Topic", "Topic", "Topic"],
    },
    {
      backgroundColor: "#D3D3D3", // Light Gray
      name: "Name",
      yearToggle: ["2023", "2024"],
      topics: [],
    },
    {
      backgroundColor: "#1E1E1E", // Black
      textColor: "#FFFFFF", // White Text
      name: "Name",
      yearToggle: ["2023", "2024"],
      topics: [],
    },
    {
      backgroundColor: "#FFFFCC", // Light Yellow
      name: "Name",
      yearToggle: ["2023", "2024"],
      topics: ["Topic", "Topic", "Topic", "Topic", "Topic", "Topic"],
    },
    {
      backgroundColor: "#E8AE77", // Peach
      name: "Name",
      yearToggle: ["2023", "2024"],
      topics: ["Topic", "Topic"],
    },
    {
      backgroundColor: "#D3D3D3", // Light Gray
      name: "Name",
      yearToggle: ["2023", "2024"],
      topics: ["Topic", "Topic", "Topic"],
    },
  ];
  return (
    <>
      <motion.div
        initial={{ x: 0, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        exit={{ x: 0, opacity: 0 }}
        transition={{ duration: 0.5, ease: "easeInOut" }}
        className="columns-1 gap-0 md:columns-2 lg:columns-3 xl:columns-4"
      >
        {data.map((item, index) => {
          return (
            <Tooltip
              key={index}
              placement="right"
              overlay={
                <>
                  <div className="text-black px-5 py-6 flex flex-col gap-4">
                    <h1 className="  font-extrabold text-[20px] leading-[100%] tracking-[-0.03em] text-center">
                      Summary
                    </h1>
                    <p className="  font-medium text-[8px] leading-[198%] tracking-[-0.04em]">
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                      Ut vel nisl tempus, placerat purus ullamcorper,
                      condimentum nunc. In vehicula mi sed nisi aliquet iaculis.
                      Aliquam efficitur turpis nec pretium pellentesque. Quisque
                      placerat elit sit amet volutpat euismod. Nunc purus metus,
                      hendrerit a orci vitae.
                    </p>
                    <div className="flex justify-end">
                      <span
                        className="w-fit px-5 bg-[#353535] cursor-pointer text-white py-2.5 rounded-[22px]   font-black text-[6px] leading-[100%] tracking-[-0.04em]"
                        onClick={() => setSelectedItem(item)}
                      >
                        View More
                      </span>
                    </div>
                  </div>
                </>
              }
            >
              <div
                style={{ backgroundColor: item.backgroundColor }}
                className="bg-[#FFE28C] break-inside-avoid p-5 cursor-pointer hover:opacity-70"
              >
                <div className="flex justify-between px-8">
                  <img src={MaskGroupIcon} alt="logo" />
                  <div className="relative">
                    <span className="absolute top-0.5 -left-6 px-5 bg-[#353535] text-white py-2.5 rounded-[22px]   font-black text-[6px] leading-[100%] tracking-[-0.04em]">
                      {item.yearToggle[0]}
                    </span>
                    <span className="px-5 ps-10 bg-white text-[#2C2C2C] py-2.5 rounded-[22px]   font-black text-[6px] leading-[100%] tracking-[-0.04em]">
                      {item.yearToggle[1]}
                    </span>
                  </div>
                </div>
                <div className="px-8 mt-10">
                  <h1 className="  font-extrabold text-[30px] leading-[100%] tracking-[-0.03em] text-start">
                    {item.name}
                    {index}
                  </h1>
                  <div className="flex flex-wrap gap-2 w-[70%] mt-4 pb-7">
                    {item.topics.map((topic, i) => (
                      <span
                        key={i}
                        className="px-5 bg-[#353535] text-white py-2.5 rounded-[22px]   font-black text-[6px] leading-[100%] tracking-[-0.04em]"
                      >
                        {topic}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </Tooltip>
          );
        })}
      </motion.div>
      <Modal
        title="Summary"
        open={!!selectedItem}
        onCancel={() => setSelectedItem(null)}
        footer={null}
        width={800}
        styles={{
          body: { maxHeight: "70vh", overflowY: "auto", fontFamily: "Inter" },
        }}
      >
        <p>
          Lorem ipsum dolor, sit amet consectetur adipisicing elit. Veritatis,
          laudantium minima. Iure recusandae optio beatae consequatur hic
          adipisci dicta aspernatur placeat impedit facere neque nobis quis eius
          odio iusto voluptatibus amet eaque, totam sunt ipsam corrupti vitae
          atque nemo. Quasi ab earum, dolore deleniti reprehenderit, facilis
          dolores quam cumque, beatae autem consequuntur! Quasi qui quia nemo,
          ex in sed dolorum suscipit consectetur perferendis incidunt eum
          nesciunt aut exercitationem velit! At, ad maxime. Necessitatibus
          dolores modi, ut perspiciatis magnam delectus nisi excepturi molestias
          asperiores doloribus autem aliquam sit quidem error repellendus
          mollitia magni. Quod asperiores sequi vel nobis quo cumque labore
          facere error et, magni voluptas autem modi iure soluta sint?
          Dignissimos nesciunt tempore magni rem iusto voluptates reprehenderit
          laudantium ducimus culpa, cumque dolor libero sunt repellendus saepe,
          sapiente aliquam possimus dolores sequi necessitatibus praesentium
          alias porro distinctio temporibus? Eaque dolore voluptatem debitis
          iure blanditiis minus perspiciatis ipsum error rem consequuntur harum,
          amet maiores sit mollitia voluptas quidem eligendi qui fuga in dicta
          est eius neque quod? Ut repudiandae placeat quod neque doloremque
          voluptatem possimus voluptate culpa, perspiciatis accusantium ducimus
          est eaque, expedita laborum provident! Unde eligendi sunt nisi
          architecto vel temporibus quo! Architecto, alias consectetur earum
          nesciunt omnis ratione iste. Lorem ipsum dolor, sit amet consectetur
          adipisicing elit. Veritatis, laudantium minima. Iure recusandae optio
          beatae consequatur hic adipisci dicta aspernatur placeat impedit
          facere neque nobis quis eius odio iusto voluptatibus amet eaque, totam
          sunt ipsam corrupti vitae atque nemo. Quasi ab earum, dolore deleniti
          reprehenderit, facilis dolores quam cumque, beatae autem consequuntur!
          Quasi qui quia nemo, ex in sed dolorum suscipit consectetur
          perferendis incidunt eum nesciunt aut exercitationem velit! At, ad
          maxime. Necessitatibus dolores modi, ut perspiciatis magnam delectus
          nisi excepturi molestias asperiores doloribus autem aliquam sit quidem
          error repellendus mollitia magni. Quod asperiores sequi vel nobis quo
          cumque labore facere error et, magni voluptas autem modi iure soluta
          sint? Dignissimos nesciunt tempore magni rem iusto voluptates
          reprehenderit laudantium ducimus culpa, cumque dolor libero sunt
          repellendus saepe, sapiente aliquam possimus dolores sequi
          necessitatibus praesentium alias porro distinctio temporibus? Eaque
          dolore voluptatem debitis iure blanditiis minus perspiciatis ipsum
          error rem consequuntur harum, amet maiores sit mollitia voluptas
          quidem eligendi qui fuga in dicta est eius neque quod? Ut repudiandae
          placeat quod neque doloremque voluptatem possimus voluptate culpa,
          perspiciatis accusantium ducimus est eaque, expedita laborum
          provident! Unde eligendi sunt nisi architecto vel temporibus quo!
          Architecto, alias consectetur earum nesciunt omnis ratione iste. Lorem
          ipsum dolor, sit amet consectetur adipisicing elit. Veritatis,
          laudantium minima. Iure recusandae optio beatae consequatur hic
          adipisci dicta aspernatur placeat impedit facere neque nobis quis eius
          odio iusto voluptatibus amet eaque, totam sunt ipsam corrupti vitae
          atque nemo. Quasi ab earum, dolore deleniti reprehenderit, facilis
          dolores quam cumque, beatae autem consequuntur! Quasi qui quia nemo,
          ex in sed dolorum suscipit consectetur perferendis incidunt eum
          nesciunt aut exercitationem velit! At, ad maxime. Necessitatibus
          dolores modi, ut perspiciatis magnam delectus nisi excepturi molestias
          asperiores doloribus autem aliquam sit quidem error repellendus
          mollitia magni. Quod asperiores sequi vel nobis quo cumque labore
          facere error et, magni voluptas autem modi iure soluta sint?
          Dignissimos nesciunt tempore magni rem iusto voluptates reprehenderit
          laudantium ducimus culpa, cumque dolor libero sunt repellendus saepe,
          sapiente aliquam possimus dolores sequi necessitatibus praesentium
          alias porro distinctio temporibus? Eaque dolore voluptatem debitis
          iure blanditiis minus perspiciatis ipsum error rem consequuntur harum,
          amet maiores sit mollitia voluptas quidem eligendi qui fuga in dicta
          est eius neque quod? Ut repudiandae placeat quod neque doloremque
          voluptatem possimus voluptate culpa, perspiciatis accusantium ducimus
          est eaque, expedita laborum provident! Unde eligendi sunt nisi
          architecto vel temporibus quo! Architecto, alias consectetur earum
          nesciunt omnis ratione iste. Lorem ipsum dolor, sit amet consectetur
          adipisicing elit. Veritatis, laudantium minima. Iure recusandae optio
          beatae consequatur hic adipisci dicta aspernatur placeat impedit
          facere neque nobis quis eius odio iusto voluptatibus amet eaque, totam
          sunt ipsam corrupti vitae atque nemo. Quasi ab earum, dolore deleniti
          reprehenderit, facilis dolores quam cumque, beatae autem consequuntur!
          Quasi qui quia nemo, ex in sed dolorum suscipit consectetur
          perferendis incidunt eum nesciunt aut exercitationem velit! At, ad
          maxime. Necessitatibus dolores modi, ut perspiciatis magnam delectus
          nisi excepturi molestias asperiores doloribus autem aliquam sit quidem
          error repellendus mollitia magni. Quod asperiores sequi vel nobis quo
          cumque labore facere error et, magni voluptas autem modi iure soluta
          sint? Dignissimos nesciunt tempore magni rem iusto voluptates
          reprehenderit laudantium ducimus culpa, cumque dolor libero sunt
          repellendus saepe, sapiente aliquam possimus dolores sequi
          necessitatibus praesentium alias porro distinctio temporibus? Eaque
          dolore voluptatem debitis iure blanditiis minus perspiciatis ipsum
          error rem consequuntur harum, amet maiores sit mollitia voluptas
          quidem eligendi qui fuga in dicta est eius neque quod? Ut repudiandae
          placeat quod neque doloremque voluptatem possimus voluptate culpa,
          perspiciatis accusantium ducimus est eaque, expedita laborum
          provident! Unde eligendi sunt nisi architecto vel temporibus quo!
          Architecto, alias consectetur earum nesciunt omnis ratione iste.
        </p>
      </Modal>
    </>
  );
};

export default CardSectionArea;

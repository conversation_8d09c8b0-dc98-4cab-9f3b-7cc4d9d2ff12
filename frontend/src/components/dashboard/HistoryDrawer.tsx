import { useState } from "react";
import flowerIcon from "../../assets/images/flowerIcon.svg";
import flowerOpenedIcon from "../../assets/images/flowerOpenedIcon.svg";
import { useNavigate } from "react-router-dom";
import { AnimatePresence, motion } from "framer-motion";
import { Modal, Popover } from "antd";
import { formatDate, formatMonth } from "../../utils";
import SearchBar from "../common/SearchBar";
import {
  deleteConversationById,
  updateConversationById,
} from "../../services/conversationService";
import { useUser } from "../../hooks/useUser";

type Props = {
  setOpenHistoryView: (value: boolean) => void;
  allConversations: [];
  activeConversation: string;
  setActiveConversation: (value: string) => void;
  getAllConversations: () => void;
  setOpenEmailSection: (value: boolean) => void;
  activeChat: string;
};

type conversationType = {
  _id: string;
  userId: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
};

const HistoryDrawer = ({
  setOpenHistoryView,
  allConversations,
  activeConversation,
  setActiveConversation,
  getAllConversations,
  setOpenEmailSection,
  activeChat,
}: Props) => {
  const navigate = useNavigate();
  const { user } = useUser();
  const [openMenu, setOpenMenu] = useState<number | null | boolean>(null);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [renameValue, setRenameValue] = useState("");
  const [updateConversation, setUpdateConversation] =
    useState<conversationType | null>(null);

  const filteredConversations = allConversations.filter(
    (item: conversationType) =>
      item.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleRenameConversation = async () => {
    setUpdateConversation(null);
    try {
      if (updateConversation) {
        await updateConversationById(updateConversation, renameValue);
        getAllConversations();
      }
    } catch (error) {
      console.error("Error:", error);
    }
    setRenameValue("");
  };

  const sendEmail = () => {
    const to = user?.email;
    const subject = encodeURIComponent("Hello from React!");
    const body = encodeURIComponent(`testing body`);

    const mailtoLink = `mailto:${to}?subject=${subject}&body=${body}`;
    window.location.href = mailtoLink;
  };

  const handleDeleteConversation = async (id: string) => {
    try {
      await deleteConversationById(id);
      setOpenMenu(false);
      getAllConversations();
    } catch (error) {
      console.error("Error:", error);
    }
  };

  return (
    <>
      <div className="relative">
        <div
          className="bg-[linear-gradient(168.12deg,_rgba(255,_255,_255,_0.4)_0%,_rgba(255,_255,_255,_0.1)_98.85%)] backdrop-blur-[40px] shadow-[0px_4px_24px_-1px_rgba(136,_136,_136,_0.12)] cursor-pointer w-fit p-1 absolute -right-3 top-4 rounded-[8px] z-50"
          style={{ backdropFilter: "blur(40px)" }}
          onClick={() => setOpenHistoryView(false)}
        >
          <img src={flowerOpenedIcon} alt="icon" />
        </div>
        <div>
          <div
            className="border-[0.3px] backdrop-blur-[40px] bg-[linear-gradient(168.12deg,rgba(255,255,255,0.4)_0%,rgba(255,255,255,0.1)_98.85%)] shadow-[0px_4px_24px_-1px_#8888881F] py-3.5 px-4 my-3.5 ms-1.5 me-6  rounded-lg bg-white/30"
            style={{
              borderImageSource:
                "linear-gradient(170.39deg, rgba(254, 218, 204, 0.75) 1.06%, rgba(254, 188, 154, 0.75) 98.07%)",
              borderImageSlice: 1,
            }}
          >
            <h1 className="text-[#222222]   font-bold text-[20px] leading-[133%] tracking-[0%]">
              History
            </h1>
          </div>
          <input
            type="text"
            className="shadow-[0px_1px_2px_0px_rgba(55,55,55,0.1)] bg-[#FFFFFF80] w-full px-2 hover:border-0 mb-4"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        {/* Cards */}
        <div className="overflow-y-auto historyList scrollbar-hide overflow-x-hidden">
          {filteredConversations.map((item: conversationType, index) => {
            return (
              <motion.div
                key={index}
                whileHover={{
                  scaleX: 1.02,
                  originX: 0,
                }}
                animate={{
                  backgroundColor:
                    item?._id === activeConversation ? "#ababab" : "#ffffff",
                }}
                onClick={() => {
                  setActiveConversation(item?._id);
                  setOpenMenu(false);
                  setOpenEmailSection(false);
                }}
                className="relative flex justify-between border-[0.2px] border-[#444444] bg-white items-center py-5 gap-4.5 pe-5 ps-9 cursor-pointer w-[274px]"
              >
                <span className="absolute top-[39px] -left-10 border-[0.2px] border-[#444444] -rotate-90 bg-[#ECECEC] py-0.5 text-[18px] mx-auto w-[40.5%] text-center">
                  {formatMonth(item?.updatedAt).toUpperCase()}
                </span>
                <span className="  font-normal text-[70px] leading-[100%] tracking-[-0.04em] w-full text-center">
                  {formatDate(item?.updatedAt)}
                </span>
                <span className="  font-light text-[20px] leading-[100%] tracking-[-0.04em] w-full truncate-title">
                  {item.title}
                </span>
                <div className="relative">
                  <Popover
                    placement="right"
                    content={
                      <>
                        <AnimatePresence>
                          {openMenu === index && (
                            <>
                              <motion.div
                                initial={{ opacity: 0, y: 0 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: 0 }}
                                transition={{
                                  duration: 0.5,
                                  ease: "easeInOut",
                                }}
                                className="items-start left-16 -bottom-6 z-50 flex flex-col gap-2 w-max font-['Inter']"
                              >
                                <button
                                  className="  font-bold text-[17px] leading-[100%] tracking-[0%] w-fit px-4 py-2.5 text-center text-white bg-[#1E1E1E] rounded-[25px] cursor-pointer"
                                  onClick={() => {
                                    setUpdateConversation(item);
                                    setRenameValue(item.title);
                                    setOpenMenu(false);
                                  }}
                                >
                                  Rename
                                </button>
                                <button
                                  className="  font-bold text-[17px] leading-[100%] tracking-[0%] w-fit px-4 py-2.5 text-center text-white bg-[#1E1E1E] rounded-[25px] cursor-pointer"
                                  onClick={() =>
                                    navigate(`/dashboard/report/${activeChat}`)
                                  }
                                >
                                  Generate Report
                                </button>
                                <button
                                  className="  font-bold text-[17px] leading-[100%] tracking-[0%] w-fit px-4 py-2.5 text-center text-white bg-[#1E1E1E] rounded-[25px] cursor-pointer"
                                  // onClick={(event) => {
                                  //   setActiveConversation(item?._id);
                                  //   event.stopPropagation();
                                  //   setOpenEmailSection(true);
                                  //   setOpenMenu(false);
                                  // }}
                                  onClick={sendEmail}
                                >
                                  Generate Email
                                </button>
                                <button
                                  className="  font-bold text-[17px] leading-[100%] tracking-[0%] w-fit px-4 py-2.5 text-center text-white bg-[#1E1E1E] rounded-[25px] cursor-pointer"
                                  onClick={() =>
                                    handleDeleteConversation(item._id)
                                  }
                                >
                                  Delete
                                </button>
                              </motion.div>
                            </>
                          )}
                        </AnimatePresence>
                      </>
                    }
                    trigger="click"
                    open={openMenu === index}
                    onOpenChange={() => setOpenMenu(!openMenu)}
                  >
                    <motion.img
                      src={flowerIcon}
                      alt="icon"
                      className="cursor-pointer"
                      whileHover={{
                        scale: 1.3,
                      }}
                      animate={{
                        rotate: openMenu === index ? 225 : 0,
                        filter:
                          openMenu === index
                            ? "brightness(0) saturate(100%)"
                            : "brightness(1) saturate(100%)",
                      }}
                      transition={{ duration: 0.5, ease: "easeInOut" }}
                      onClick={(event) => {
                        event.stopPropagation();
                        if (openMenu === index) {
                          setOpenMenu(null);
                        } else {
                          setOpenMenu(index);
                        }
                      }}
                    />
                  </Popover>
                </div>
              </motion.div>
            );
          })}
          {filteredConversations.length < 1 && (
            <>
              <p className="text-center">No Result Found</p>
            </>
          )}
        </div>
      </div>
      <Modal
        title={`Update Conversation - ${
          updateConversation ? updateConversation.title : ""
        }`}
        open={!!updateConversation}
        footer={null}
        onCancel={() => setUpdateConversation(null)}
        styles={{
          body: { maxHeight: "70vh", overflowY: "auto" },
        }}
      >
        <div className="m-5">
          <SearchBar
            color="#1E1E1E"
            resize={false}
            border={false}
            handleSearch={handleRenameConversation}
            setInputValue={setRenameValue}
            inputValue={renameValue}
            placeholder="conversation"
            disable={!renameValue.length}
          />
        </div>
      </Modal>
    </>
  );
};

export default HistoryDrawer;

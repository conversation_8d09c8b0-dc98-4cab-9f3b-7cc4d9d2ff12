import { useState } from "react";
import flowerIcon from "../../assets/images/flowerIcon.svg";
import magnifyIcon from "../../assets/images/magnifyIcon.svg";
import SearchBar from "../common/SearchBar";
import { AnimatePresence, motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import EmailSection from "./emailSection/EmailSection";
import arrowIcon from "../../assets/images/arrowIcon.svg";
import { useUser } from "../../hooks/useUser";

const FooterSection = ({
  messages,
  handleSearch,
  setInputValue,
  inputValue,
  setOpenEmailSection,
  openEmailSection,
  reply,
  activeChat,
  handlePrevious,
  handleNext,
  maxPair,
  activePairIndex
}: {
  reply: string;
  messages: { content: string }[];
  handleSearch: () => void;
  setInputValue: (value: string) => void;
  inputValue: string;
  setOpenEmailSection: (value: boolean) => void;
  openEmailSection: boolean;
  activeChat?: string;
  handlePrevious: () => void;
  handleNext: () => void;
  activePairIndex:number;
  maxPair: number;
}) => {
  const navigate = useNavigate();
  const [isOpenMenu, setisOpenMenu] = useState(false);
  const { user } = useUser();
  const sendEmail = () => {
    const to = user?.email;
    const subject = encodeURIComponent("Hello from React!");
    const body = encodeURIComponent(`testing body`);

    const mailtoLink = `mailto:${to}?subject=${subject}&body=${body}`;
    window.location.href = mailtoLink;
  };
  return (
    <>
      <div className="bg-[#414141] mx-auto rounded-[10px] pt-18 md:pt-24">
        <div className="flex justify-between px-6 md:px-16 pb-5 md:pb-8">
          <p className=" text-[12px] sm:text-[15px] leading-normal sm:leading-[32px] tracking-[0.01em] font-normal text-white w-[90%] max-h-[220px] overflow-y-auto scrollbar-custom pl-6">
            {messages[1]?.content || reply}
          </p>

          <div className="relative h-fit">
            <motion.img
              src={flowerIcon}
              alt="icon"
              className="cursor-pointer w-[40px] h-[40px]"
              animate={{
                rotate: isOpenMenu ? 225 : 0,
                filter: isOpenMenu
                  ? "brightness(0) saturate(100%)"
                  : "brightness(1) saturate(100%)",
              }}
              transition={{ duration: 0.5, ease: "easeInOut" }}
              onClick={() => setisOpenMenu(!isOpenMenu)}
            />
            <AnimatePresence>
              {isOpenMenu && (
                <>
                  <motion.div
                    initial={{ opacity: 0, y: 0 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 0 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                    className="absolute -right-2 bottom-18 md:-left-16 md:bottom-18 items-center xl:left-16 xl:bottom-10 z-50 flex flex-col gap-2 w-max font-['Inter']"
                  >
                    <button
                      className="  font-bold text-[17px] leading-[100%] tracking-[0%] w-fit px-4 py-2.5 text-center text-white bg-[#1E1E1E] rounded-[25px] cursor-pointer"
                      onClick={() => navigate(`/dashboard/report/${activeChat}`)}
                    >
                      Generate Report
                    </button>
                    <button
                      className="  font-bold text-[17px] leading-[100%] tracking-[0%] w-fit px-4 py-2.5 text-center text-white bg-[#1E1E1E] rounded-[25px] cursor-pointer"
                      onClick={() => {
                        // setOpenEmailSection(true);
                        sendEmail();
                        setisOpenMenu(false);
                      }}
                    >
                      Generate Email
                    </button>
                  </motion.div>
                </>
              )}
            </AnimatePresence>

            <AnimatePresence>
              {openEmailSection && (
                <>
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 bg-white/50 backdrop-blur-sm z-[499]"
                    onClick={() => setOpenEmailSection(false)}
                  />
                  <EmailSection setOpenEmailSection={setOpenEmailSection} />
                </>
              )}
            </AnimatePresence>
          </div>
        </div>
        <div className="flex justify-around">
        {/* <button className="cursor-pointer bg-white shadow-[0px_2px_6.7px_0px_rgba(0,0,0,0.25)] rounded-[10px] px-4 py-2" onClick={()=>handlePrevious()}>
          <img src={arrowIcon} alt="icon" height={17} width={20} className="rotate-180 invert"/></button> */}

          <button
              className={`bg-[#353535] h-full flex items-center justify-center w-1/2 py-4 ${activePairIndex > 0 ?"cursor-pointer":"cursor-not-allowed"}`}
              onClick={()=>handlePrevious()}
            >
              <img
                src={arrowIcon}
                alt="icon"
                height={17}
                width={20}
                className="flex-shrink-0 rotate-180 "
              />
            </button>
             <button
              className={`bg-[#1E1E1E] h-full flex items-center justify-center w-1/2 py-4 ${activePairIndex < maxPair - 1?"cursor-pointer":"cursor-not-allowed"}`}
              onClick={()=>handleNext()}
            >
              <img
                src={arrowIcon}
                alt="icon"
                height={17}
                width={20}
                className="flex-shrink-0"
              />
            </button>
        {/* <button className="cursor-pointer bg-white shadow-[0px_2px_6.7px_0px_rgba(0,0,0,0.25)] rounded-[10px] px-4 py-2" onClick={()=>handleNext()}>
          <img src={arrowIcon} alt="icon" height={17} width={20} className="invert" /></button> */}
        </div>
      </div>
      <div className="text-[17px] bg-white leading-[100%] tracking-[0] text-center font-normal absolute top-0 left-0 shadow-[0px_2px_6.7px_0px_rgba(0,0,0,0.25)] w-full rounded-[10px] pb-4 pt-6 flex justify-center gap-4 px-8">
        <img src={magnifyIcon} alt="icon" className="hidden sm:block" />
        <p className="text-[14px] sm:text-[16px] px-6 min-h-5 max-h-[20px] md:max-h-[36px] overflow-y-auto scrollbar-custom">
          {messages[0]?.content}
        </p>
        <div className="absolute -top-11 w-[90%] sm:w-[60%]">
          <SearchBar
            color="#424242"
            resize={false}
            border={true}
            handleSearch={handleSearch}
            setInputValue={setInputValue}
            inputValue={inputValue}
            placeholder="write your message here..."
            disable={!inputValue.length}
          />
        </div>
      </div>
    </>
  );
};

export default FooterSection;

//

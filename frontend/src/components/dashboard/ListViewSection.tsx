import ListCards from "./ListCards";
import { useState } from "react";

const ListViewSection = ({referenceData}:any) => {
  
  // const params = new URLSearchParams(location.search);
  // const category = params.get("category");
  // console.log("category", category);
  const [backupData,setBackUpData] = useState([]);

 
  //   const listCardsData: any = [
  //     {
  //       date: {
  //         year: "2023",
  //         day: "23",
  //         month: "OCT",
  //       },
  //       category: "Category",
  //       summary: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut vel
  // nisl tempus, placerat purus ullamcorper, condimentum nunc. In
  // vehicula mi sed nisi aliquet iaculis. Aliquam efficitur turpis
  // nec pretium pellentesque. Quisque placerat elit sit amet
  // volutpat euismod. Nunc purus metus, hendrerit a orci vitae,
  // rutrum aliquam libero. Pellentesque vitae scelerisque nunc.
  // Phasellus scelerisque libero in nibh mattis, non eleifend mauris
  // imperdiet. In quis pulvinar magna, id dapibus lorem. Quisque
  // sollicitudin magna ac cursus gravida. In ex arcu, blandit sed
  // placerat sed, eleifend quis orci. Vestibulum porta lectus a
  // ligula hendrerit, euismod. Nunc purus metus, hendrerit a orci
  // vitae, rutrum aliquam libero. Pellentesque vitae scelerisque
  // nunc. Phasellus scelerisque libero in nibh mattis, non eleifend
  // mauris imperdiet. In quis pulvinar magna, id dapibus lorem.
  // Quisque sollicitudin magna ac cursus gravida. In ex arcu,
  // blandit sed placerat sed, eleifend quis orci. Vestibulum porta
  // lectus a ligula hendrerit,`,
  //       keywords: [
  //         "Keyword1",
  //         "Keyword2",
  //         "Keyword3",
  //         "Keyword4",
  //         "Keyword5",
  //         "Keyword6",
  //         "Keyword7",
  //         "Keyword8",
  //         "Keyword9",
  //         "Keyword10",
  //       ],
  //       importantItems: [
  //         `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut vel
  // nisl tempus, placerat purus ullamcorper, condimentum nunc.`,
  //         `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut vel
  // nisl tempus, placerat purus ullamcorper, condimentum nunc.`,
  //       ],
  //     },
  //   ];
  return (
    <>
      {referenceData.length>0? referenceData?.map((item: any, index: number) => {
        return (
          <ListCards key={index} data={item} setBackUpData={setBackUpData} referenceData={referenceData} backupData={backupData}/>
        );
      }):<div className="flex justify-center mt-6">No References Found</div>}
    </>
  );
};

export default ListViewSection;

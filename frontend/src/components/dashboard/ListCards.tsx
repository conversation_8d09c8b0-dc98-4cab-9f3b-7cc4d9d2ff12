import Modal from "antd/es/modal/Modal";
import infoIcon from "../../assets/images/InfoIcon.svg";
// import arrowIcon from "../../assets/images/arrowIcon.svg";
import { useState } from "react";
// import { formatReferenceDate } from "../../utils";
// import { useLocation } from "react-router-dom";
// import { useLocation, useNavigate } from "react-router-dom";

const ListCards = ({
  data,
}: // setReferenceData,
// referenceData,
// backupData,
// setBackUpData,
any) => {
  console.log("data", data);
  const {
    // Category,
    summary,
    keywords:Keywords,
    key_points: Important_items,
    // Effective_end_date,
    // children,
    // Effective_start_date,
    // parent,
    // references,
  } = data;
  // const location = useLocation();
  // const { references } = location.state;
  // const navigate = useNavigate();
  // const params = new URLSearchParams(location.search);
  // const category = params.get("category");
  // console.log("category", category);
  const [selectedItem, setSelectedItem] = useState<null | [] | object>(null);
  const [summaryData, setSummarydata] = useState("");
  const [impItems, setImpItems] = useState([]);
  const [keywords, setKeywords] = useState([]);

  const handleCloseModal = () => {
    setSelectedItem(null);
    setSummarydata("");
    setImpItems([]);
    setKeywords([]);
  };

  // const handleNext = () => {
  //   console.log("xxxxxxnext", children);
  //   if (children.length) {
  //     const matchingChildren = references.filter((item: any) => {
  //       return children.includes(item.id);
  //     });
  //     console.log("matchingChildren", matchingChildren);
  //     setBackUpData(referenceData);
  //     setReferenceData(matchingChildren);
  //   }
  // };

  // const handlePrev = () => {
  //   // const matchingParent = references.filter((item: any) => parent === item.id);
  //   // const matchingChildren = references.filter((item: any) =>
  //   //   matchingParent[0]?.children?.includes(item.id)
  //   // );

  //   // console.log("matchingParent", matchingChildren);
  //   if (Category === "Piece") {
  //     setReferenceData(backupData);
  //   } else {
  //     setReferenceData(references);
  //   }
  // };

  return (
    <>
      <div className="flex border-t-4 border-[#939393]">
        {/* <div className=" hidden lg:flex w-[26%] lg:w-[20%]  pb-7 flex-col gap-10 items-center">
          <div className="relative pt-7">
            <span className="absolute top-7  px-6 py-3 bg-[#353535] text-white rounded-[10px] flex flex-col w-fit gap-1 items-center">
              <span className="  font-black text-[6px] leading-[100%] tracking-[-0.04em]">
                {formatReferenceDate(Effective_start_date)?.year}
              </span>
              <span className="  font-bold text-[30px] leading-[100%] tracking-[-0.04em]">
                {formatReferenceDate(Effective_start_date)?.day}
              </span>
              <span className="  font-light text-[12px] leading-[100%] tracking-[-0.04em]">
                {formatReferenceDate(Effective_start_date)?.month}
              </span>
            </span>
            <span className="px-5 ps-25 bg-[#F7F7F7] text-[#2C2C2C] py-3 rounded-[10px]  flex flex-col w-fit gap-1 items-center">
              <span className="  font-black text-[6px] leading-[100%] tracking-[-0.04em]">
                {formatReferenceDate(Effective_start_date)?.year}
              </span>
              <span className="  font-bold text-[30px] leading-[100%] tracking-[-0.04em]">
                {formatReferenceDate(Effective_start_date)?.day}
              </span>
              <span className="  font-light text-[12px] leading-[100%] tracking-[-0.04em]">
                {formatReferenceDate(Effective_start_date)?.month}
              </span>
            </span>
          </div>
          <div className="  font-medium text-[20px] leading-[100%] tracking-[-0.04em] px-5 bg-[#353535] text-white py-2.5 rounded-[10px] w-fit">
            {Category}
          </div>
        </div> */}

        <div className="hidden lg:block w-[60%] border-x-[0.5px] border-[#9E9E9E]">
          <div className="text-black px-5 py-6 flex flex-col gap-4 ps-12">
            <h1 className="  font-semibold text-[9px] leading-[100%] tracking-[-0.04em]">
              Summary
            </h1>
            <p className="  font-medium text-[8px] leading-[198%] tracking-[-0.04em] truncate-text">
              {summary}
            </p>

            <div className="flex justify-end">
              <span
                className="w-fit px-5 bg-[#353535] text-white py-2.5 rounded-[22px]   font-black text-[6px] leading-[100%] tracking-[-0.04em] cursor-pointer"
                onClick={() => {
                  setSelectedItem(data);
                  setSummarydata(summary);
                }}
              >
                View More
              </span>
            </div>
          </div>
        </div>

        <div className="w-[100%] lg:w-[46%] border-x-[0.5px] lg:border-s-0  border-[#9E9E9E]">
          <div className="ps-2 lg:ps-0">
            <div className="flex border-b-[0.5px] border-[#9E9E9E] lg:hidden">
              {/* <div className="w-[50%] sm:w-[30%] flex flex-col items-center gap-3 p-3">
                <div className="relative pt-7">
                  <span className="absolute top-7 px-4 sm:px-6 py-2 sm:py-3 bg-[#353535] text-white rounded-[10px] flex flex-col w-fit gap-0.5 sm:gap-1 items-center">
                    <span className="  font-black text-[6px] leading-[100%] tracking-[-0.04em]">
                      {formatReferenceDate(Effective_start_date)?.year}
                    </span>
                    <span className="  font-bold text-[26px] sm:text-[30px] leading-[100%] tracking-[-0.04em]">
                      {formatReferenceDate(Effective_start_date)?.day}
                    </span>
                    <span className="  font-light text-[12px] leading-[100%] tracking-[-0.04em]">
                      {formatReferenceDate(Effective_start_date)?.month}
                    </span>
                  </span>
                  <span className="px-4 sm:px-5 ps-18 sm:ps-25 bg-[#F7F7F7] text-[#2C2C2C] py-2 sm:py-3 rounded-[10px]  flex flex-col w-fit gap-0.5 sm:gap-1 items-center">
                    <span className="  font-black text-[6px] leading-[100%] tracking-[-0.04em]">
                      {formatReferenceDate(Effective_start_date)?.year}
                    </span>
                    <span className="  font-bold text-[26px] sm:text-[30px] leading-[100%] tracking-[-0.04em]">
                      {formatReferenceDate(Effective_start_date)?.day}
                    </span>
                    <span className="  font-light text-[12px] leading-[100%] tracking-[-0.04em]">
                      {formatReferenceDate(Effective_start_date)?.month}
                    </span>
                  </span>
                </div>
                <div className="  font-medium text-[20px] leading-[100%] tracking-[-0.04em] px-5 bg-[#353535] text-white py-2.5 rounded-[10px] w-fit">
                  {Category}
                </div>
              </div> */}
              <div className="w-full relative text-black   border-[#9E9E9E]  px-5 py-6 flex flex-col gap-3 ">
                <h1 className="  font-semibold text-[9px] leading-[100%] tracking-[-0.04em]">
                  Summary
                </h1>

                <p className="  font-medium text-[8px] leading-[198%] tracking-[-0.04em] truncate-text">
                  {summary}
                </p>

                <span
                  className="w-fit px-2.5 bg-[#353535] text-white py-1 rounded-[8.5px]   font-medium text-[5px] leading-[100%] tracking-[-0.04em] absolute right-2.5 bottom-2 cursor-pointer"
                  onClick={() => {
                    setSelectedItem(data);
                    setSummarydata(summary);
                  }}
                >
                  View More
                </span>
              </div>
            </div>
            <div className="relative text-black px-5 py-6 flex flex-col gap-3 border-b-[0.5px]  border-[#9E9E9E]">
              <h1 className="  font-semibold text-[9px] leading-[100%] tracking-[-0.04em]">
                Keywords
              </h1>

              <div className="flex flex-wrap gap-2 w-full sm:w-[85%] ">
                {Keywords?.map((item: any, i: number) => (
                  <>
                   { i < 3 && <span
                      key={i}
                      className="px-5 bg-[#353535] text-white py-2 rounded-[22px]   font-black text-[6px] leading-[100%] tracking-[-0.04em]"
                    >
                      {item}
                    </span>}
                  </>
                ))}
              </div>

              <span
                className="w-fit px-2.5 bg-[#353535] text-white py-1 rounded-[8.5px]   font-medium text-[5px] leading-[100%] tracking-[-0.04em] absolute right-2.5 bottom-2.5 cursor-pointer"
                onClick={() => {
                  setSelectedItem(data);
                  setKeywords(Keywords);
                }}
              >
                View More
              </span>
            </div>
            <div className="relative text-black px-5 py-6 flex flex-col gap-3">
              <h1 className="  font-semibold text-[9px] leading-[100%] tracking-[-0.04em]">
                Important Items
              </h1>
              {Important_items?.map((item: any, index: number) => {
                return (
                  <>
                    {index < 2 && (
                      <div className="flex gap-2.5" key={index}>
                        <img src={infoIcon} alt="icon" />
                        <p className="  font-medium text-[8px] leading-[198%] tracking-[-0.04em] text-[#222222] truncate-item">
                          {item}
                        </p>
                      </div>
                    )}
                  </>
                );
              })}
              <span
                className="w-fit px-2.5 bg-[#353535] text-white py-1 rounded-[8.5px]   font-medium text-[5px] leading-[100%] tracking-[-0.04em] absolute right-2.5 bottom-2.5 cursor-pointer"
                onClick={() => {
                  setSelectedItem(data);
                  setImpItems(Important_items);
                }}
              >
                View More
              </span>
            </div>
          </div>

          {/* <div className="bg-[#1E1E1E] flex md:hidden h-[50px]">
            <div
              className={`flex h-full items-center justify-center w-1/2 ${
                Category === "Piece" ? "cursor-not-allowed" : "cursor-pointer"
              }`}
              onClick={handlePrev}
            >
              <img
                src={arrowIcon}
                alt="icon"
                height={17}
                width={20}
                className="rotate-180 flex-shrink-0"
              />
            </div>
            <button
              className={`bg-[#424242] rounded-s-[39.5px] h-full flex items-center justify-center w-1/2 ${
                Category === "Set" ? "cursor-not-allowed" : "cursor-pointer"
              }`}
              onClick={handleNext}
            >
              <img
                src={arrowIcon}
                alt="icon"
                height={17}
                width={20}
                className="flex-shrink-0"
              />
            </button>
          </div> */}
        </div>

        {/* <div className="w-[4%] bg-[#1E1E1E] hidden md:block">
          <button
            className={`bg-[#424242]  w-full cursor-pointer  items-center justify-center ${
              Category === "Piece" ? "hidden" : "flex"
            }
            ${Category === "Set" ? "h-full " : "h-[50%]  rounded-b-[39.5px]"}`}
            onClick={handleNext}
          >
            <img
              src={arrowIcon}
              alt="icon"
              height={17}
              width={20}
              className="flex-shrink-0"
            />
          </button>
          <div
            className={`  items-center justify-center cursor-pointer ${
              Category === "Piece" ? "h-full" : "h-[50%]"
            } ${Category === "Set" ? "hidden" : "flex"}`}
            onClick={handlePrev}
          >
            <img
              src={arrowIcon}
              alt="icon"
              height={17}
              width={20}
              className="rotate-180 flex-shrink-0"
            />
          </div>
        </div> */}
      </div>

      <Modal
        title={
          summaryData
            ? "summary"
            : impItems.length
            ? "Important Items"
            : keywords.length
            ? "Keywords"
            : ""
        }
        open={!!selectedItem}
        onCancel={handleCloseModal}
        footer={null}
        width={800}
        styles={{
          body: { maxHeight: "70vh", overflowY: "auto", fontFamily: "Inter" },
        }}
      >
        <p className="py-5">
          {summaryData ? (
            summaryData
          ) : impItems.length ? (
            <>
              {impItems.map((item: any, index: number) => {
                return (
                  <div className="flex gap-2.5 mb-2" key={index}>
                    <img src={infoIcon} alt="icon" />
                    <p className="  font-medium text-[8px] leading-[198%] tracking-[-0.04em] text-[#222222] truncate-item">
                      {item}
                    </p>
                  </div>
                );
              })}
            </>
          ) : keywords.length ? (
            <>
              {keywords?.map((_: any, i: number) => (
                <span
                  key={i}
                  className="px-5 bg-[#353535] text-white py-2 rounded-[22px]   font-black text-[6px] leading-[100%] tracking-[-0.04em] ml-4"
                >
                  Keyword
                </span>
              ))}
            </>
          ) : (
            ""
          )}
        </p>
      </Modal>
    </>
  );
};

export default ListCards;

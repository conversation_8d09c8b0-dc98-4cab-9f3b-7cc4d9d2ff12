import { motion } from "framer-motion";

const EmailSection = ({ setOpenEmailSection }: { setOpenEmailSection: (value: boolean) => void }) => {
  return (
    <>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-500 flex items-center justify-center p-4 sm:p-0"
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="bg-white/5 backdrop-blur-xl w-[95%] sm:w-[85%] lg:w-[60%] shadow-lg flex flex-col sm:flex-row rounded-xl overflow-hidden "
        >
          <div className="w-full p-4 sm:px-12 sm:pt-12 bg-black/70 backdrop-blur-md border-b sm:border-b-0 sm:border-r border-white/10">
            <div className="flex flex-col gap-4 h-full">
              <div className="space-y-3 font-['Inter']">
                <input
                  type="email"
                  placeholder="From"
                  className="w-full p-3 rounded-lg bg-white/90 border border-white/20 text-black text-[15px] outline-none font-['Inter']"
                />
                <input
                  type="email"
                  placeholder="To"
                  className="w-full p-3 rounded-lg bg-white/90 border border-white/20 text-black text-[15px] outline-none font-['Inter']"
                />
                <input
                  type="text"
                  placeholder="Subject"
                  className="w-full p-3 rounded-lg bg-white/90 border border-white/20 text-black text-[15px] outline-none font-['Inter']"
                />
              </div>
              <textarea
                placeholder="Write your email here..."
                className="flex-1 w-full p-3 rounded-lg bg-white/90 border border-white/20 text-black text-[15px] outline-none resize-none min-h-[300px] font-['Inter']"
              />
              <div className="flex justify-end gap-3 py-2">
                <button
                  onClick={() => setOpenEmailSection(false)}
                  className="px-4 py-2 rounded-lg bg-white text-black cursor-pointer text-[15px] font-['Inter'] font-medium"
                >
                  Cancel
                </button>
                <button className="px-4 py-2 rounded-lg bg-black text-white cursor-pointer text-[15px] font-['Inter'] font-medium">
                  Send
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </>
  );
};

export default EmailSection;

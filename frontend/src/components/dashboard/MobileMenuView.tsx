import { AnimatePresence, motion } from "framer-motion";
import { CloseOutlined } from "@ant-design/icons";
import { Link } from "react-router-dom";

type Props = {
  menuOpen: boolean;
  setMenuOpen: (value: boolean) => void;
  handleSignOut: () => void;
  setShowAccountModal: (show: boolean) => void;
};

const MobileMenuView = ({ setMenuOpen, menuOpen, handleSignOut,setShowAccountModal }: Props) => {

  const navLinks = [
    { name: "Home", path: "/search" },
    { name: "Account", path: "/" },
    { name: "Sign Out", path: "/" },
  ];
  return (
    <>
      <AnimatePresence>
        {menuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -100 }}
            transition={{ duration: 0.5, ease: "easeIn" }}
            className="lg:hidden absolute z-100 top-0 left-0 w-full bg-white shadow-[0px_3px_8.4px_0px_rgba(0,0,0,0.25)]"
          >
            <div className="flex justify-end px-5 pt-4">
              <CloseOutlined
                className="text-[26px]"
                onClick={() => setMenuOpen(!menuOpen)}
              />
            </div>
            <div className="flex flex-col items-center gap-6 py-6">
              {navLinks.map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, x: 100 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{
                    duration: 0.3 * index + 1,
                    ease: [0.39, 0.575, 0.565, 1],
                  }}
                >
                  {item.name === "Account" ? (
                    <button
                      className="text-lg"
                      onClick={() => {
                        setShowAccountModal(true);
                        setMenuOpen(false);
                      }}
                    >
                      {item.name}
                    </button>
                  ) : (
                    <Link
                      to={item.path}
                      className="text-lg"
                      onClick={() => {
                        if (item.name === "Sign Out") {
                          handleSignOut();
                        } else {
                          setMenuOpen(false);
                        }
                      }}
                    >
                      {item.name}
                    </Link>
                  )}
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default MobileMenuView;

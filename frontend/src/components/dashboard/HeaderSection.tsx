import headingTitle from "../../assets/images/headingImg.svg";
import settingsIcon from "../../assets/images/settingsIcon.svg";
// import masonaryIcon from "../../assets/images/masonaryIcon.svg";
// import listLayoutIcon from "../../assets/images/listLayoutIcon.svg";
import { useNavigate } from "react-router-dom";
import { MenuOutlined } from "@ant-design/icons";
import { useState } from "react";
import MobileMenuView from "./MobileMenuView";
import { AnimatePresence, motion } from "framer-motion";
import { logOut } from "../../services/authService";
import Loader from "../common/Loader";
import AccountModal from "../homepage/account/AccountModal";

// type Props = {
//   isListView: boolean;
//   setIsListView: (value: boolean) => void;
// };

const HeaderSection = () => {
  const navigate = useNavigate();
  const [showAccountModal, setShowAccountModal] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const [openSettingMenu, setOpenSettingMenu] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const handleSignOut = () => {
    setIsLoading(true);
    logOut()
      .then(() => {
        localStorage.removeItem("authToken");
      })
      .catch((error) => {
        console.error("User fetch error:", error);
      })
      .finally(() => {
        setIsLoading(false);
        navigate("/");
      });
  };
  return (
    <>
      <div className="flex justify-between shadow-[0px_3px_8.4px_0px_rgba(0,0,0,0.25)] px-6 sm:px-14 py-8 sm:py-11">
        <div className="flex-col gap-2.5 flex-1 hidden lg:flex"></div>
        <div className="flex-1 flex justify-start lg:justify-center">
          <img
            src={headingTitle}
            alt="heading"
            onClick={() => navigate("/search")}
            className="cursor-pointer invert w-[200px] h-[50px]"
          />
        </div>
        <div className="lg:flex justify-end gap-2.5 items-center flex-1 hidden">
          {/* remove grid view */}
          {/* {isListView ? (
            <img
              src={masonaryIcon}
              alt="masonaryIcon"
              loading="lazy"
              height={27}
              width={27}
              className="cursor-pointer"
              onClick={() => setIsListView(false)}
            />
          ) : (
            <img
              src={listLayoutIcon}
              alt="listLayoutIcon"
              loading="lazy"
              height={27}
              width={27}
              className="cursor-pointer"
              onClick={() => setIsListView(true)}
            />
          )} */}

          <div className="relative flex">
            <motion.img
              src={settingsIcon}
              alt="settingsIcon"
              loading="lazy"
              height={32}
              width={32}
              className="cursor-pointer invert"
              animate={{
                rotate: openSettingMenu ? -225 : 0,
              }}
              transition={{ duration: 0.5, ease: "easeInOut" }}
              onClick={() => {
                setOpenSettingMenu(!openSettingMenu);
              }}
            />
            <AnimatePresence>
              {openSettingMenu && (
                <>
                  <motion.div
                    initial={{ opacity: 0, y: 0 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 0 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                    className="absolute -bottom-24 -right-[30px] items-center z-50 flex flex-col gap-2 w-max font-['Inter']"
                  >
                    <button
                      className="  font-bold text-[17px] leading-[100%] tracking-[0%] w-fit px-4 py-2.5 text-center text-white bg-[#1E1E1E] rounded-[25px] cursor-pointer"
                      onClick={() => {
                        setShowAccountModal(true);
                        setOpenSettingMenu(false);
                      }}
                    >
                      Account
                    </button>
                    <button
                      className="  font-bold text-[17px] leading-[100%] tracking-[0%] w-fit px-4 py-2.5 text-center text-white bg-[#1E1E1E] rounded-[25px] cursor-pointer"
                      onClick={handleSignOut}
                    >
                      Sign out
                    </button>
                  </motion.div>
                </>
              )}
            </AnimatePresence>
          </div>
        </div>

        <div className="flex justify-end gap-2.5 flex-1 lg:hidden">
          <MenuOutlined
            className="text-[26px]"
            onClick={() => setMenuOpen(!menuOpen)}
          />
        </div>
        {/* Mobile Menu */}
        <MobileMenuView
          menuOpen={menuOpen}
          setMenuOpen={setMenuOpen}
          handleSignOut={handleSignOut}
          setShowAccountModal={setShowAccountModal}
        />
        <Loader loading={isLoading} />
        <AnimatePresence>
          {showAccountModal && (
            <>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[499]"
                onClick={() => setShowAccountModal(false)}
              />
              <AccountModal setShowAccountModal={setShowAccountModal} />
            </>
          )}
        </AnimatePresence>
      </div>
    </>
  );
};

export default HeaderSection;

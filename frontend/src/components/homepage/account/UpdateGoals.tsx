import { useState, useEffect } from "react";
import { message } from "antd";
import { AxiosError } from "axios";
import {
  addGoals,
  deleteGoal,
  updateGoal,
} from "../../../services/accountService";
import { DeleteOutlined, EditOutlined, SwapOutlined } from "@ant-design/icons";
import { useGoal } from "../../../hooks/useGoal";

type Props = {
  setShowAccountModal: (show: boolean) => void;
};

type Goal = {
  _id: string;
  title: string;
  description: string;
};

type AddEditGoalProps = {
  editingGoalId: string | null;
  formData: { title: string; description: string };
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  setShowGoalsList: (show: boolean) => void;
  setShowAccountModal: (show: boolean) => void;
  resetForm: () => void;
  handleSubmit: () => void;
  isLoading: boolean;
};

const AddEditGoal = ({
  editingGoalId,
  formData,
  handleChange,
  setShowGoalsList,
  setShowAccountModal,
  resetForm,
  handleSubmit,
  isLoading,
}: AddEditGoalProps) => (
  <div className="flex flex-col h-full">
    <div className="flex justify-between px-6 my-8">
      <h2 className="text-3xl font-bold">
        {editingGoalId ? "Edit Goal" : "Add Goal"}
      </h2>
      <button
        onClick={() => setShowGoalsList(true)}
        className="flex items-center gap-2 px-4 py-2 text-sm bg-black text-white rounded-lg hover:bg-gray-800 cursor-pointer"
      >
        <SwapOutlined /> View All Goals
      </button>
    </div>

    <div className="px-6">
      <div className="flex flex-col gap-5 mb-6">
        <input
          type="text"
          name="title"
          placeholder="Enter Title"
          value={formData?.title}
          onChange={handleChange}
          className="rounded-[10px] w-full p-4 text-sm bg-[#9C9C9C]/[0.28] backdrop-blur-[30.3px] text-black placeholder:text-black/70 outline-none"
        />
        <input
          type="text"
          name="description"
          placeholder="Enter Description"
          value={formData?.description}
          onChange={handleChange}
          className="rounded-[10px] w-full p-4 text-sm bg-[#9C9C9C]/[0.28] backdrop-blur-[30.3px] text-black placeholder:text-black/70 outline-none"
        />
      </div>
    </div>

    <div className="mt-auto p-4 border-t border-gray-200 flex justify-between gap-2 sm:gap-4">
      <button
        onClick={() => {
          if (editingGoalId) {
            resetForm();
          } else {
            setShowAccountModal(false);
          }
        }}
        className="px-4 sm:px-6 py-2 text-sm sm:text-base text-gray-600 hover:text-gray-800 cursor-pointer"
        disabled={isLoading}
      >
        {editingGoalId ? "Cancel Edit" : "Cancel"}
      </button>
      <button
        onClick={handleSubmit}
        disabled={isLoading}
        className="px-4 sm:px-6 py-2 text-sm sm:text-base bg-black text-white rounded-lg hover:bg-gray-800 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? "Saving..." : editingGoalId ? "Update Goal" : "Add Goal"}
      </button>
    </div>
  </div>
);

const UpdateGoals = ({ setShowAccountModal }: Props) => {
  const { goals, refetch } = useGoal();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({ title: "", description: "" });
  const [editingGoalId, setEditingGoalId] = useState<string | null>(null);
  const [showGoalsList, setShowGoalsList] = useState(false);

  useEffect(() => {
    refetch();
  }, [refetch]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleEdit = (goal: Goal) => {
    setFormData({ title: goal.title, description: goal.description });
    setEditingGoalId(goal._id);
  };

  const resetForm = () => {
    setFormData({ title: "", description: "" });
    setEditingGoalId(null);
  };

  const handleSubmit = async () => {
    if (!formData.title || !formData.description) {
      message.error("Please fill in all fields");
      return;
    }

    try {
      setIsLoading(true);
      if (editingGoalId) {
        await updateGoal(editingGoalId, formData);
        message.success("Goal updated successfully");
      } else {
        await addGoals(formData);
        message.success("Goal added successfully");
      }
      await refetch();
      resetForm();
    } catch (error: unknown) {
      message.error(
        error instanceof AxiosError
          ? error.response?.data?.message
          : "Failed to save Goal"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteGoal(id);
      await refetch();
      message.success("Goal deleted successfully");
    } catch (error: unknown) {
      console.log("error", error);
      message.error("Failed to delete Goal");
    }
  };

  const QuotesList = () => (
    <div className="flex flex-col h-full">
      <div className="flex flex-col sm:flex-row justify-between px-6 my-8 gap-4">
        <h2 className="text-3xl font-bold">Goals</h2>
        <button
          onClick={() => setShowGoalsList(false)}
          className="flex items-center justify-center gap-2 px-4 py-2 text-sm bg-black text-white rounded-lg hover:bg-gray-800 cursor-pointer"
        >
          <SwapOutlined /> Add New Goal
        </button>
      </div>

      <div className="flex-1 px-6 overflow-hidden">
        <div className="overflow-y-auto h-full pr-2">
          <div className="space-y-3">
            {goals.map((goal) => (
              <div
                key={goal._id}
                className="bg-white/50 backdrop-blur-sm rounded-lg p-4 flex flex-col sm:flex-row sm:items-center gap-4"
              >
                <div className="flex-1">
                  <p className="text-gray-800">{goal.title}</p>
                  <p className="text-sm text-gray-600 mt-1">
                    - {goal.description}
                  </p>
                </div>
                <div className="flex gap-3 w-full sm:w-auto">
                  <button
                    className="flex-1 sm:flex-initial flex items-center justify-center gap-2 px-3 py-1.5 text-sm bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100"
                    onClick={() => {
                      handleEdit(goal);
                      setShowGoalsList(false);
                    }}
                  >
                    <EditOutlined /> Edit
                  </button>
                  <button
                    className="flex-1 sm:flex-initial flex items-center justify-center gap-2 px-3 py-1.5 text-sm bg-red-50 text-red-600 rounded-lg hover:bg-red-100"
                    onClick={() => handleDelete(goal._id)}
                  >
                    <DeleteOutlined /> Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return showGoalsList ? (
    <QuotesList />
  ) : (
    <AddEditGoal
      editingGoalId={editingGoalId}
      formData={formData}
      handleChange={handleChange}
      setShowGoalsList={setShowGoalsList}
      setShowAccountModal={setShowAccountModal}
      resetForm={resetForm}
      handleSubmit={handleSubmit}
      isLoading={isLoading}
    />
  );
};

export default UpdateGoals;

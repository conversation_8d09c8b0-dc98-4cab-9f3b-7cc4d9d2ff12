import { useState } from "react";
import { message } from "antd";
import { updateUserInfo } from "../../../services/accountService";
import { useUser } from "../../../hooks/useUser";

type Props = {
  setShowAccountModal: (show: boolean) => void;
};

const AccountInfoUpdate = ({ setShowAccountModal }: Props) => {
  const { user, setUser } = useUser();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    firstName: user?.firstName || "",
    lastName: user?.lastName || "",
    username: user?.username || "",
    email: user?.email || "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async () => {
    try {
      setIsLoading(true);
      const response = await updateUserInfo(formData);
      if (response.data) {
        setUser(response.data);
        message.success("Account information updated successfully");
        setShowAccountModal(false);
      }
    } catch (error: unknown) {
      const err = error as { response?: { data?: { message?: string } } };
      message.error(err.response?.data?.message || "An error occurred");
      console.error("Update error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1">
        <div className="flex justify-between px-6 my-8">
          <h2 className="text-3xl font-bold">Account Information</h2>
        </div>
        <div className="my-6 px-6">
          <div className="flex flex-col gap-5 my-4">
            <input
              type="text"
              name="firstName"
              placeholder="First Name"
              value={formData.firstName}
              onChange={handleChange}
              className="rounded-[10px] w-full p-4 text-sm bg-[#9C9C9C]/[0.28] backdrop-blur-[30.3px] text-black placeholder:text-black/70 outline-none"
            />
            <input
              type="text"
              name="lastName"
              placeholder="Last Name"
              value={formData.lastName}
              onChange={handleChange}
              className="rounded-[10px] w-full p-4 text-sm bg-[#9C9C9C]/[0.28] backdrop-blur-[30.3px] text-black placeholder:text-black/70 outline-none"
            />
            <input
              type="text"
              name="username"
              placeholder="User Name"
              value={formData.username}
              onChange={handleChange}
              className="rounded-[10px] w-full p-4 text-sm bg-[#9C9C9C]/[0.28] backdrop-blur-[30.3px] text-black placeholder:text-black/70 outline-none"
            />
            <input
              type="email"
              name="email"
              placeholder="Email"
              value={formData.email}
              onChange={handleChange}
              className="rounded-[10px] w-full p-4 text-sm bg-[#9C9C9C]/[0.28] backdrop-blur-[30.3px] text-black placeholder:text-black/70 outline-none"
            />
          </div>
        </div>
      </div>
      <div className="mt-auto p-4 border-t border-gray-200 flex justify-between gap-2 sm:gap-4">
        <button
          onClick={() => setShowAccountModal(false)}
          className="px-4 sm:px-6 py-2 text-sm sm:text-base text-gray-600 hover:text-gray-800 cursor-pointer"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          onClick={handleSubmit}
          disabled={isLoading}
          className="px-4 sm:px-6 py-2 text-sm sm:text-base bg-black text-white rounded-lg hover:bg-gray-800 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? "Saving..." : "Save Changes"}
        </button>
      </div>
    </div>
  );
};

export default AccountInfoUpdate;

import { useEffect } from "react";
import { message } from "antd";
import { DeleteOutlined, UploadOutlined } from "@ant-design/icons";
import {
  deleteImage,
  uploadImage,
} from "../../../services/adminService";
import { useImages } from "../../../hooks/useImages";

const UpdatePictures = () => {
  const { imageUrls, refetch } = useImages();

  const handleUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      await uploadImage(e.target.files[0]);
      message.success("Image uploaded successfully");
      refetch();
    }
  };

  const handleDelete = async (url: string) => {
    try {
      await deleteImage(url);
      refetch();
      message.success("Picture deleted successfully");
    } catch (error: unknown) {
      console.log('error', error)
      message.error("Failed to delete picture");
    }
  };

  useEffect(() => {
    refetch();
  }, [refetch]);

  return (
    <div className="flex flex-col h-full">
      <div className="flex flex-col sm:flex-row justify-between px-6 my-8 gap-4">
        <h2 className="text-3xl font-bold">Pictures</h2>
        <label className="flex items-center justify-center gap-2 px-4 py-2 text-sm bg-black text-white rounded-lg hover:bg-gray-800 cursor-pointer">
          <UploadOutlined />
          Upload Image
          <input
            type="file"
            accept="image/*"
            onChange={handleUpload}
            className="hidden"
          />
        </label>
      </div>

      <div className="flex-1 px-6 overflow-hidden">
        <div className="overflow-y-auto h-full pr-2">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {imageUrls.map((picture) => (
              <div
                key={picture.id}
                className="bg-white/50 backdrop-blur-sm rounded-lg p-4"
              >
                <img
                  src={picture.url}
                  alt="img"
                  className="w-full h-48 object-cover rounded-lg mb-3"
                />
                <div className="flex gap-3">
                  <button
                    className="flex-1 flex items-center justify-center gap-2 px-3 py-1.5 text-sm bg-red-50 text-red-600 rounded-lg hover:bg-red-100"
                    onClick={() => handleDelete(picture.url)}
                  >
                    <DeleteOutlined /> Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UpdatePictures;

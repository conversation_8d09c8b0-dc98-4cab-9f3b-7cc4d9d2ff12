import { useState, useEffect } from "react";
import { message } from "antd";
import { AxiosError } from "axios";
import {
  addQuotes,
  deleteQuote,
  updateQuote,
} from "../../../services/accountService";
import { DeleteOutlined, EditOutlined, SwapOutlined } from "@ant-design/icons";
import { useQuotes } from "../../../hooks/useQuotes";

type Props = {
  setShowAccountModal: (show: boolean) => void;
};

type Quote = {
  _id: string;
  quote: string;
  author: string;
};

type AddEditQuoteProps = {
  editingQuoteId: string | null;
  formData: { author: string; quote: string };
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  setShowQuotesList: (show: boolean) => void;
  setShowAccountModal: (show: boolean) => void;
  resetForm: () => void;
  handleSubmit: () => void;
  isLoading: boolean;
};

const AddEditQuote = ({
  editingQuoteId,
  formData,
  handleChange,
  setShowQuotesList,
  setShowAccountModal,
  resetForm,
  handleSubmit,
  isLoading,
}: AddEditQuoteProps) => (
  <div className="flex flex-col h-full">
    <div className="flex justify-between px-6 my-8">
      <h2 className="text-3xl font-bold">
        {editingQuoteId ? "Edit Quote" : "Add Quote"}
      </h2>
      <button
        onClick={() => setShowQuotesList(true)}
        className="flex items-center gap-2 px-4 py-2 text-sm bg-black text-white rounded-lg hover:bg-gray-800 cursor-pointer"
      >
        <SwapOutlined /> View All Quotes
      </button>
    </div>

    <div className="px-6">
      <div className="flex flex-col gap-5 mb-6">
        <input
          type="text"
          name="quote"
          placeholder="Enter Quote"
          value={formData?.quote}
          onChange={handleChange}
          className="rounded-[10px] w-full p-4 text-sm bg-[#9C9C9C]/[0.28] backdrop-blur-[30.3px] text-black placeholder:text-black/70 outline-none"
        />
        <input
          type="text"
          name="author"
          placeholder="Author Name"
          value={formData?.author}
          onChange={handleChange}
          className="rounded-[10px] w-full p-4 text-sm bg-[#9C9C9C]/[0.28] backdrop-blur-[30.3px] text-black placeholder:text-black/70 outline-none"
        />
      </div>
    </div>

    <div className="mt-auto p-4 border-t border-gray-200 flex justify-between gap-2 sm:gap-4">
      <button
        onClick={() => {
          if (editingQuoteId) {
            resetForm();
          } else {
            setShowAccountModal(false);
          }
        }}
        className="px-4 sm:px-6 py-2 text-sm sm:text-base text-gray-600 hover:text-gray-800 cursor-pointer"
        disabled={isLoading}
      >
        {editingQuoteId ? "Cancel Edit" : "Cancel"}
      </button>
      <button
        onClick={handleSubmit}
        disabled={isLoading}
        className="px-4 sm:px-6 py-2 text-sm sm:text-base bg-black text-white rounded-lg hover:bg-gray-800 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading
          ? "Saving..."
          : editingQuoteId
          ? "Update Quote"
          : "Add Quote"}
      </button>
    </div>
  </div>
);

const UpdateQuotes = ({ setShowAccountModal }: Props) => {
  const { quotes, refetch } = useQuotes();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({ author: "", quote: "" });
  const [editingQuoteId, setEditingQuoteId] = useState<string | null>(null);
  const [showQuotesList, setShowQuotesList] = useState(false);

  useEffect(() => {
    refetch();
  }, [refetch]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleEdit = (quote: Quote) => {
    setFormData({ quote: quote.quote, author: quote.author });
    setEditingQuoteId(quote._id);
  };

  const resetForm = () => {
    setFormData({ quote: "", author: "" });
    setEditingQuoteId(null);
  };

  const handleSubmit = async () => {
    if (!formData.quote || !formData.author) {
      message.error("Please fill in all fields");
      return;
    }

    try {
      setIsLoading(true);
      if (editingQuoteId) {
        await updateQuote(editingQuoteId, formData);
        message.success("Quote updated successfully");
      } else {
        await addQuotes(formData);
        message.success("Quote added successfully");
      }
      await refetch();
      resetForm();
    } catch (error: unknown) {
      message.error(
        error instanceof AxiosError
          ? error.response?.data?.message
          : "Failed to save quote"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteQuote(id);
      await refetch();
      message.success("Quote deleted successfully");
    } catch (error: unknown) {
      console.log("error", error);
      message.error("Failed to delete quote");
    }
  };

  const QuotesList = () => (
    <div className="flex flex-col h-full">
      <div className="flex flex-col sm:flex-row justify-between px-6 my-8 gap-4">
        <h2 className="text-3xl font-bold">Quotes</h2>
        <button
          onClick={() => setShowQuotesList(false)}
          className="flex items-center justify-center gap-2 px-4 py-2 text-sm bg-black text-white rounded-lg hover:bg-gray-800 cursor-pointer"
        >
          <SwapOutlined /> Add New Quote
        </button>
      </div>

      <div className="flex-1 px-6 overflow-hidden">
        <div className="overflow-y-auto h-full pr-2">
          <div className="space-y-3">
            {quotes.map((quote) => (
              <div
                key={quote._id}
                className="bg-white/50 backdrop-blur-sm rounded-lg p-4 flex flex-col sm:flex-row sm:items-center gap-4"
              >
                <div className="flex-1">
                  <p className="text-gray-800">{quote.quote}</p>
                  <p className="text-sm text-gray-600 mt-1">- {quote.author}</p>
                </div>
                <div className="flex gap-3 w-full sm:w-auto">
                  <button
                    className="flex-1 sm:flex-initial flex items-center justify-center gap-2 px-3 py-1.5 text-sm bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100"
                    onClick={() => {
                      handleEdit(quote);
                      setShowQuotesList(false);
                    }}
                  >
                    <EditOutlined /> Edit
                  </button>
                  <button
                    className="flex-1 sm:flex-initial flex items-center justify-center gap-2 px-3 py-1.5 text-sm bg-red-50 text-red-600 rounded-lg hover:bg-red-100"
                    onClick={() => handleDelete(quote._id)}
                  >
                    <DeleteOutlined /> Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return showQuotesList ? (
    <QuotesList />
  ) : (
    <AddEditQuote
      editingQuoteId={editingQuoteId}
      formData={formData}
      handleChange={handleChange}
      setShowQuotesList={setShowQuotesList}
      setShowAccountModal={setShowAccountModal}
      resetForm={resetForm}
      handleSubmit={handleSubmit}
      isLoading={isLoading}
    />
  );
};

export default UpdateQuotes;

import { motion } from "framer-motion";
import { useState } from "react";
import AccountInfoUpdate from "./AccountInfoUpdate";
import UpdatePassword from "./UpdatePassword";
import UpdateQuotes from "./UpdateQuotes";
import UpdatePictures from "./UpdatePictures";
import UpdateGoals from "./UpdateGoals";

type Props = {
  setShowAccountModal: (value: boolean) => void;
};

const AccountModal = ({ setShowAccountModal }: Props) => {
  const [activeSection, setActiveSection] = useState("accountInfo");

  const handleSectionClick = (section: string) => {
    setActiveSection(section);
  };
  return (
    <>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-500 flex items-center justify-center p-4 sm:p-0"
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="bg-white/5 backdrop-blur-xl w-[95%] sm:w-[85%] lg:w-[70%] shadow-lg flex flex-col sm:flex-row rounded-xl overflow-hidden max-h-[90vh] sm:min-h-[600px] sm:max-h-[600px]"
        >
          <div className="w-full sm:w-[30%] p-4 sm:p-5 bg-black/70 backdrop-blur-md border-b sm:border-b-0 sm:border-r border-white/10">
            <h2 className="text-lg sm:text-xl font-bold text-white my-4 sm:my-8 flex justify-between">
              Account
              <button
                onClick={() => setShowAccountModal(false)}
                className="text-gray-500 hover:text-gray-700 text-xl sm:text-2xl cursor-pointer block sm:hidden"
              >
                ×
              </button>
            </h2>

            <div className="text-[#AAAAAA] my-4 sm:my-6">
              <ul className="flex flex-row sm:flex-col gap-4 sm:gap-6 overflow-x-auto sm:overflow-visible text-[14px] sm:text-[16px]">
                <li
                  className={`cursor-pointer w-max whitespace-nowrap ${
                    activeSection === "accountInfo" ? "text-white" : ""
                  }`}
                  onClick={() => handleSectionClick("accountInfo")}
                >
                  Account Information
                </li>
                <li
                  className={`cursor-pointer w-max whitespace-nowrap ${
                    activeSection === "updatePassword" ? "text-white" : ""
                  }`}
                  onClick={() => handleSectionClick("updatePassword")}
                >
                  Update Password
                </li>
                <li
                  className={`cursor-pointer w-max whitespace-nowrap ${
                    activeSection === "pictures" ? "text-white" : ""
                  }`}
                  onClick={() => handleSectionClick("pictures")}
                >
                  Gallery
                </li>
                <li
                  className={`cursor-pointer w-max whitespace-nowrap ${
                    activeSection === "quotes" ? "text-white" : ""
                  }`}
                  onClick={() => handleSectionClick("quotes")}
                >
                  Quote Library
                </li>
                <li
                  className={`cursor-pointer w-max whitespace-nowrap ${
                    activeSection === "goals" ? "text-white" : ""
                  }`}
                  onClick={() => handleSectionClick("goals")}
                >
                  Goals
                </li>
              </ul>
            </div>
          </div>
          <div className="w-full sm:w-[70%] p-4 sm:p-5 bg-white/90 backdrop-blur-md relative flex flex-col h-[70vh] sm:h-auto">
            <div className="sm:flex justify-end mb-2 hidden">
              <button
                onClick={() => setShowAccountModal(false)}
                className="text-gray-500 hover:text-gray-700 text-xl sm:text-2xl cursor-pointer"
              >
                ×
              </button>
            </div>
            <div className="flex-1 overflow-y-auto">
              {activeSection === "accountInfo" && (
                <AccountInfoUpdate setShowAccountModal={setShowAccountModal} />
              )}
              {activeSection === "updatePassword" && (
                <UpdatePassword setShowAccountModal={setShowAccountModal} />
              )}
              {activeSection === "pictures" && <UpdatePictures />}
              {activeSection === "quotes" && (
                <UpdateQuotes setShowAccountModal={setShowAccountModal} />
              )}
              {activeSection === "goals" && (
                <UpdateGoals setShowAccountModal={setShowAccountModal} />
              )}
            </div>
          </div>
        </motion.div>
      </motion.div>
    </>
  );
};

export default AccountModal;

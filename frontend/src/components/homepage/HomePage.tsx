import headingImg from "../../assets/images/headingImg.svg";
import Heading from "./Heading";
import Footer from "./Footer";
import SearchBar from "../common/SearchBar";
import { motion } from "framer-motion";
import { createConversation } from "../../services/conversationService";
import { createChat } from "../../services/chatService";
import { createMessage } from "../../services/messageService";
import { createReference } from "../../services/referenceService";
import { useNavigate } from "react-router-dom";
import { useEffect, useRef, useState } from "react";
// import dummyData from "../../assets/dummyData.json";
import { Carousel } from "antd";
import {
  LinkOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
} from "@ant-design/icons";
import { Modal, message } from "antd";
import { FileOutlined } from "@ant-design/icons";
import {
  // AudioIcon,
  // CameraIcon,
  // LinkIcon,
  ShareIcon,
} from "../icons/CustomIcons";
import { useImages } from "../../hooks/useImages";
import { useLoader } from "../../hooks/useLoader";
import useWindowResize from "../../hooks/useWindowResize";

const HomePage = () => {
  const { setLoading } = useLoader();
  const { imageUrls, refetch } = useImages();
  // const fileInputRef = useRef<HTMLInputElement | null>(null);
  const documentInputRef = useRef<HTMLInputElement | null>(null);
  const navigate = useNavigate();
  const [inputValue, setInputValue] = useState<string>("");
  // const streamRef = useRef<MediaStream | null>(null);
  const [recording, ] = useState<boolean>(false);
  // const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  // const audioChunks = useRef<Blob[]>([]);
  const [attachments, setAttachments] = useState<
    Array<{
      file: File | null;
      preview: string | null;
      name: string;
      type: string;
    }>
  >([]);
  const [isUrlModalOpen, setIsUrlModalOpen] = useState(false);
  const [urlInput, setUrlInput] = useState("");
  const [playingAudio, setPlayingAudio] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const { screenWidth } = useWindowResize();

  // const handleAudioStop = () => {
  //   if (mediaRecorderRef.current) {
  //     mediaRecorderRef.current.stop();
  //     setRecording(false);
  //   }
  //   if (streamRef.current) {
  //     streamRef.current.getTracks().forEach((track) => track.stop());
  //     streamRef.current = null;
  //   }
  // };

  // const startRecording = async () => {
  //   try {
  //     const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
  //     streamRef.current = stream;

  //     const mediaRecorder = new MediaRecorder(stream);
  //     mediaRecorderRef.current = mediaRecorder;

  //     mediaRecorder.ondataavailable = (event: BlobEvent) => {
  //       audioChunks.current.push(event.data);
  //     };

  //     mediaRecorder.onstop = () => {
  //       const audioBlob = new Blob(audioChunks.current, { type: "audio/wav" });
  //       const url = URL.createObjectURL(audioBlob);
  //       const file = new File([audioBlob], "voice-message.wav", {
  //         type: "audio/wav",
  //       });

  //       setAttachments((prev) => [
  //         ...prev,
  //         {
  //           file,
  //           preview: url,
  //           name: file.name,
  //           type: "audio",
  //         },
  //       ]);
  //       audioChunks.current = [];
  //     };

  //     mediaRecorder.start();
  //     setRecording(true);
  //   } catch (error) {
  //     console.error("Microphone access error:", error);
  //   }
  // };

  // const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
  //   const file = event.target.files?.[0];
  //   if (!file) return;
  //   if (file && file.type.startsWith("image/")) {
  //     const reader = new FileReader();
  //     reader.onloadend = () => {
  //       setAttachments((prev) => [
  //         ...prev,
  //         {
  //           file,
  //           preview: reader.result as string,
  //           name: file.name,
  //           type: "image",
  //         },
  //       ]);
  //     };
  //     reader.readAsDataURL(file);
  //   }
  // };

  const handleDocumentChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    if (file) {
      setAttachments((prev) => [
        ...prev,
        {
          file,
          preview: "doc",
          name: file.name,
          type: (file.name.split(".").pop() || "").toLowerCase(),
        },
      ]);
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  };

  // const handleLinkAttach = () => {
  //   setIsUrlModalOpen(true);
  // };

  const handleUrlSubmit = () => {
    if (!urlInput.trim()) {
      message.error("Please enter a valid URL");
      return;
    }

    try {
      new URL(urlInput);
      setAttachments((prev) => [
        ...prev,
        {
          file: null,
          preview: "url",
          name: urlInput,
          type: "url",
        },
      ]);
      setUrlInput("");
      setIsUrlModalOpen(false);
    } catch {
      message.error("Please enter a valid URL");
    }
  };

  // const handleImageClick = () => {
  //   console.log("Image click");
  //   if (fileInputRef.current) {
  //     fileInputRef.current.click();
  //   }
  // };

  const handleDocumentClick = () => {
    console.log("Document click");
    if (documentInputRef.current) {
      documentInputRef.current.click();
    }
  };

  const handleSearch = async () => {
    try {
      setLoading(true);
      const payload = {
        title: "Conversation",
        attachedFiles: {
          image: attachments.filter(
            (attachment) => attachment.type === "image"
          ),
          audio: attachments.filter(
            (attachment) => attachment.type === "audio"
          ),
          url: attachments.filter((attachment) => attachment.preview === "url"),
          file: attachments.filter(
            (attachment) => attachment.preview === "doc"
          ),
        },
      };
      console.log("payload", payload, attachments);
      if (inputValue && !recording) {
        const conversationData = await createConversation({
          title: inputValue,
        });

        const chatData = await createChat({
          title: "Chat",
          conversationId: conversationData?.data?._id,
        });

        const messageData = await createMessage({
          chatId: chatData?.data?._id,
          content: inputValue,
          sender: "user",
          attachedFiles: {
            image: attachments.filter(
              (attachment) => attachment.type === "image"
            ),
            audio: attachments.filter(
              (attachment) => attachment.type === "audio"
            ),
            url: attachments.filter(
              (attachment) => attachment.preview === "url"
            ),
            file: attachments.filter(
              (attachment) => attachment.preview === "doc"
            ),
          },
        });

         await createReference({
          messageId: messageData?.data?.aiMessageCreated?._id,
          content:messageData?.data?.references
          // type: "document",
          // sourceUrl: "https://example.com/article/12345",
        });
        // navigate("/dashboard");
        navigate(`/dashboard`, {
          // state: {
          //   references: messageData?.data?.references,
          //   messageReply: messageData?.data?.aiMessageCreated.content,
          // },
        });
        // navigate("/dashboard?category=single document", {
        //   state: { references: messageData?.data?.references },
        // });
      }
    } catch (error) {
      console.error("Error:", error);
      setLoading(false);
    }
  };

  const handlePaste = async (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
    const items = event.clipboardData?.items;

    if (!items) return;

    for (const item of Array.from(items)) {
      // Handle URLs and text
      if (item.type === "text/plain") {
        const text = await new Promise<string>((resolve) => {
          item.getAsString((s) => resolve(s));
        });

        // Check if pasted content is URL
        try {
          new URL(text);
          setAttachments((prev) => [
            ...prev,
            {
              file: null,
              preview: "url",
              name: text,
              type: "url",
            },
          ]);
          return;
        } catch {
          // Not a URL, use as normal input
          setInputValue(text);
        }
      }

      // Handle images
      if (item.type.startsWith("image/")) {
        const file = item.getAsFile();
        if (!file) continue;

        const reader = new FileReader();
        reader.onloadend = () => {
          setAttachments((prev) => [
            ...prev,
            {
              file,
              preview: reader.result as string,
              name: file.name || "Pasted image",
              type: "image",
            },
          ]);
        };
        reader.readAsDataURL(file);
        return;
      }

      // Handle other files
      if (item.type.indexOf("application/") === 0) {
        const file = item.getAsFile();
        if (!file) continue;

        setAttachments((prev) => [
          ...prev,
          {
            file,
            preview: "doc",
            name: file.name,
            type: (file.name.split(".").pop() || "").toLowerCase(),
          },
        ]);
        return;
      }
    }
  };

  useEffect(() => {
    refetch();
  }, [refetch]);

  const handleAudioPlay = (url: string) => {
    if (playingAudio === url) {
      audioRef.current?.pause();
      setPlayingAudio(null);
    } else {
      if (audioRef.current) {
        audioRef.current.src = url;
        audioRef.current.play();
        setPlayingAudio(url);
      }
    }
  };

  return (
    <>
      <motion.div
        initial={{ x: 0, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        exit={{ x: 0, opacity: 0 }}
        transition={{ duration: 0.5, ease: "easeInOut" }}
        className="relative h-[100vh] w-full home-section"
      >
        {imageUrls && imageUrls.length > 0 ? (
          <>
            <Carousel
              autoplay
              dots={screenWidth > 769 ? true : false}
              className=" w-full"
            >
              {imageUrls?.map((item: { url: string }, index: number) => {
                return (
                  <div key={index}>
                    <img
                      src={item?.url}
                      alt="carouselImg"
                      className="h-[100vh] w-full object-cover"
                    />
                  </div>
                );
              })}
            </Carousel>
          </>
        ) : (
          <div className="h-[100vh] w-full object-cover bg-[#1E1E1E]" />
        )}

        <div className="absolute top-0 left-0 w-full h-full flex flex-col justify-between">
          <Heading />
          <div className="flex flex-col gap-0 sm:gap-4.5 w-full items-center">
            <img
              src={headingImg}
              alt="headingImg"
              loading="eager"
              className="mix-blend-screen w-[620px] h-[120px] px-12 sm:px-20"
            />
            <div className="w-[90%] sm:w-[70%] lg:w-[45%] font-['Charter']">
              <div className={`bg-[#F6F6F6] rounded-[25px] px-4 py-4`}>
                {attachments.length > 0 && (
                  <div className="mb-2 flex flex-wrap gap-2">
                    {attachments.map((attachment, index) => (
                      <div
                        key={index}
                        className="bg-white/80 rounded-lg p-1 pr-2 flex items-center gap-2 max-w-[128px]"
                      >
                        {attachment.type === "url" ? (
                          <div className="h-8 w-8 flex-shrink-0 flex items-center justify-center bg-blue-50 rounded">
                            <LinkOutlined
                              style={{ fontSize: "20px", color: "#1890ff" }}
                            />
                          </div>
                        ) : attachment.type === "audio" ? (
                          <div
                            className="h-8 w-8 flex-shrink-0 flex items-center justify-center bg-red-50 rounded cursor-pointer"
                            onClick={() =>
                              handleAudioPlay(attachment.preview || "")
                            }
                          >
                            {playingAudio === attachment.preview ? (
                              <PauseCircleOutlined
                                style={{ fontSize: "20px", color: "#ff4d4f" }}
                              />
                            ) : (
                              <PlayCircleOutlined
                                style={{ fontSize: "20px", color: "#ff4d4f" }}
                              />
                            )}
                          </div>
                        ) : attachment.preview === "doc" ? (
                          <div className="h-8 w-8 flex-shrink-0 flex items-center justify-center bg-gray-100 rounded">
                            <FileOutlined style={{ fontSize: "20px" }} />
                          </div>
                        ) : (
                          <img
                            src={attachment.preview || ""}
                            alt="preview"
                            className="h-8 w-8 flex-shrink-0 object-cover rounded"
                          />
                        )}
                        <span className="text-xs text-gray-700 min-w-0 flex-1 truncate">
                          {attachment.name}
                        </span>
                        <button
                          onClick={() => removeAttachment(index)}
                          className="ml-1 flex-shrink-0 text-gray-500 hover:text-gray-700"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
                <SearchBar
                  color="#1E1E1E"
                  border={false}
                  resize={true}
                  handleSearch={handleSearch}
                  inputValue={inputValue}
                  setInputValue={setInputValue}
                  placeholder="write your message here"
                  disable={!inputValue.length || recording}
                  onPaste={(event) =>
                    handlePaste(
                      event as unknown as React.ClipboardEvent<HTMLInputElement>
                    )
                  }
                />
                <div className="mb-2 mt-4 flex flex-wrap gap-4 justify-end">
                  <div
                    className="bg-white p-2 px-4 rounded-[10px] cursor-pointer shadow-sm"
                    onClick={() =>{
                      const token: any = localStorage.getItem("authToken");
                      window.open(`https://deep.mergenai.io/chat?token=${encodeURIComponent(token)}`)
                    }
                    }
                  >
                    Deep Research
                  </div>
                  {/* <div
                    className="bg-white p-2 rounded-[10px] cursor-pointer shadow-sm"
                    onClick={handleImageClick}
                  >
                    <CameraIcon />
                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={handleFileChange}
                      accept="image/*"
                      className="hidden"
                    />
                  </div>
                  <motion.div
                    className={`p-2 rounded-[10px] cursor-pointer shadow-sm ${
                      recording ? "bg-[#1E1E1E]" : "bg-white"
                    }`}
                    onClick={recording ? handleAudioStop : startRecording}
                    animate={
                      recording
                        ? {
                            scale: [1, 1.1, 1],
                          }
                        : {
                            scale: 1,
                          }
                    }
                    transition={
                      recording
                        ? {
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "easeInOut",
                          }
                        : {}
                    }
                  >
                    <AudioIcon color={recording ? "white" : "#1E1E1E"} />
                  </motion.div>

                  <div
                    className="bg-white p-2 rounded-[10px] cursor-pointer shadow-sm "
                    onClick={handleLinkAttach}
                  >
                    <LinkIcon color="black" />
                  </div> */}
                  <div
                    className="bg-white p-2 rounded-[10px] cursor-pointer shadow-sm "
                    onClick={handleDocumentClick}
                  >
                    <ShareIcon />
                    <input
                      type="file"
                      ref={documentInputRef}
                      onChange={handleDocumentChange}
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.txt"
                      className="hidden"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <Footer />
        </div>
      </motion.div>
      <audio
        ref={audioRef}
        onEnded={() => setPlayingAudio(null)}
        className="hidden"
      />
      <Modal
        title="Add URL"
        open={isUrlModalOpen}
        onCancel={() => {
          setIsUrlModalOpen(false);
          setUrlInput("");
        }}
        footer={null}
        styles={{
          body: { maxHeight: "70vh" },
        }}
      >
        <div className="m-5">
          <SearchBar
            color="#1E1E1E"
            border={false}
            resize={false}
            handleSearch={handleUrlSubmit}
            setInputValue={setUrlInput}
            inputValue={urlInput}
            placeholder="Enter your Url here..."
            disable={!urlInput.length}
          />
        </div>
      </Modal>
    </>
  );
};

export default HomePage;

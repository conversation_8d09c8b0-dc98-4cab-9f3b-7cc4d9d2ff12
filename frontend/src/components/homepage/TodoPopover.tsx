import { motion } from "framer-motion";
import crossIcon from "../../assets/images/crossIcon.svg";
import bgIcon from "../../assets/images/todobgIcon.svg";
import { Checkbox, Dropdown, Menu } from "antd";
import { MoreOutlined } from "@ant-design/icons";
import SwitchButton from "./SwitchButton";
import { useEffect, useState } from "react";
import {
  createAiTodoList,
  createTodoList,
  deleteAiTodoById,
  deleteTodoById,
  getAiTodoList,
  getTodoList,
  updateAiTodoById,
  updateTodoById,
} from "../../services/todoListService";
import { BeatLoader } from "react-spinners";

type Props = {
  setOpenTodoPop: (value: boolean) => void;
};

const TodoPopover = ({ setOpenTodoPop }: Props) => {
  const [loading, setLoading] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [inputAiValue, setInputAiValue] = useState("");

  const [isChecked, setIsChecked] = useState(false);
  const [todoData, setTodoData] = useState<[]>([]);
  const [aiTodoData, setAiTodoData] = useState<[]>([]);
  const [activeEdit, setActiveEdit] = useState("");
  const [activeAiEdit, setActiveAiEdit] = useState("");
  const [editValue, setEditValue] = useState("");
  const [editAiValue, setAiEditValue] = useState("");
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  const createTodo = async () => {
    try {
      setLoading(true);
      await createTodoList(inputValue, false);
      getToDo();
    } catch (error) {
      console.error("Error:", error);
    }
  };

  const getToDo = async () => {
    try {
      setLoading(true);
      const todoList = await getTodoList();
      setTodoData(todoList?.data);
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  const deleteTodo = async (id: string) => {
    try {
      setLoading(true);
      await deleteTodoById(id);
      getToDo();
    } catch (error) {
      console.error("Error:", error);
    }
  };

  const updateTodo = async (id: string, completed: boolean, value: string) => {
    try {
      setLoading(true);
      await updateTodoById(value, completed, id);
      getToDo();
    } catch (error) {
      console.error("Error:", error);
    }
  };

  const createAiTodo = async () => {
    try {
      setLoading(true);
      await createAiTodoList(inputAiValue, false);
      getAiToDo();
    } catch (error) {
      console.error("Error:", error);
    }
  };

  const getAiToDo = async () => {
    try {
      setLoading(true);
      const todoList = await getAiTodoList();
      setAiTodoData(todoList?.data);
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  const deleteAiTodo = async (id: string) => {
    try {
      setLoading(true);
      await deleteAiTodoById(id);
      getAiToDo();
    } catch (error) {
      console.error("Error:", error);
    }
  };

  const updateAiTodo = async (
    id: string,
    completed: boolean,
    value: string
  ) => {
    try {
      setLoading(true);
      await updateAiTodoById(value, completed, id);
      getAiToDo();
    } catch (error) {
      console.error("Error:", error);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      createTodo();
      setInputValue("");
    }
  };

  const handleAiChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputAiValue(e.target.value);
  };

  const handleAiKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      createAiTodo();
      setInputAiValue("");
    }
  };

  const handleAiEditKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      updateAiTodo(activeAiEdit, false, editAiValue);
      setAiEditValue("");
      setActiveAiEdit("");
    }
  };

  const handleAiEditChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAiEditValue(e.target.value);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleEditChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditValue(e.target.value);
  };

  const handleEditKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      updateTodo(activeEdit, false, editValue);
      setEditValue("");
      setActiveEdit("");
    }
  };

  useEffect(() => {
    getToDo();
    getAiToDo();
  }, []);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <>
      <motion.div
        initial={{ x: 0, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        exit={{ x: 0, opacity: 0 }}
        transition={{ duration: 0.5, ease: "easeInOut" }}
        className={`${
          isChecked ? "bg-[#2A2A2A]" : "bg-[#F2F2F2]"
        } shadow-[1px_3px_3px_0px_rgba(0,0,0,0.25)] rounded-[10px]
        w-[90%] ${!isMobile ? "w-[260px] h-[374px] " : "sm:w-[70%]"} h-[80%] 
        ${
          isMobile
            ? "fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
            : "absolute top-0 right-16"
        } 
        bg-todolist ${isMobile ? "z-[9999]" : "z-500"}`}
      >
        {loading && (
          <>
            <div className="absolute inset-0 flex items-center justify-center bg-white/10 backdrop-blur-xs">
              <BeatLoader color="#000" size={10} />
            </div>
          </>
        )}

        <div className="flex px-5 pt-4 items-center justify-between">
          <div className="flex-1 flex justify-center">
            <SwitchButton isChecked={isChecked} setIsChecked={setIsChecked} />
          </div>
          <img
            src={crossIcon}
            alt=""
            loading="lazy"
            className={`cursor-pointer ${!isChecked ? "invert" : ""}`}
            onClick={() => setOpenTodoPop(false)}
          />
        </div>

        <div className="flex px-5 py-4.5 justify-center">
          <h1
            className={`text-[15px] font-normal leading-[100%] tracking-normal ${
              isChecked ? "text-white" : "text-[#000000]"
            } `}
          >
            {isChecked ? "Mergen tasks" : "To do list"}
          </h1>
        </div>
        <div className="px-5 flex gap-3 flex-col text-black max-h-[58%] overflow-auto scrollbar-custom">
          {isChecked ? (
            <>
              {aiTodoData.map((item: any) => {
                const menu = (
                  <Menu
                    items={[
                      {
                        key: "edit",
                        label: "Edit",
                        onClick: () => setActiveAiEdit(item._id),
                      },
                      {
                        key: "delete",
                        label: "Delete",
                        onClick: () => deleteAiTodo(item._id),
                      },
                    ]}
                  />
                );
                return (
                  <>
                    <div>
                      {activeAiEdit === item?._id ? (
                        <input
                          className="border-none outline-none bg-[#D9D9D9]/10 hover:border-none p-1 text-[12px] font-normal leading-[100%] tracking-normal my-2 w-[-webkit-fill-available] text-white"
                          defaultValue={item?.description}
                          onChange={handleAiEditChange}
                          onKeyDown={handleAiEditKeyDown}
                        />
                      ) : (
                        <div className="flex justify-between text-white">
                          <div className="flex gap-2">
                            <div className="bg-[#000000] rounded-[50%] p-0.5 w-fit">
                              <img src={bgIcon} alt="icon" />
                            </div>
                            <p className="  text-[12px] font-normal leading-[100%] tracking-normal flex items-center">
                              {item?.description}
                            </p>
                          </div>
                          <Dropdown
                            menu={{ items: menu.props.items }}
                            trigger={["click"]}
                            getPopupContainer={(triggerNode) =>
                              triggerNode.parentElement as HTMLElement
                            }
                          >
                            <MoreOutlined className="cursor-pointer" />
                          </Dropdown>
                        </div>
                      )}
                    </div>
                  </>
                );
              })}
            </>
          ) : (
            <>
              {todoData.map(
                (
                  item: {
                    description: string;
                    _id: string;
                    completed: boolean;
                  },
                  index
                ) => {
                  const menu = (
                    <Menu
                      items={[
                        {
                          key: "edit",
                          label: "Edit",
                          onClick: () => setActiveEdit(item._id),
                        },
                        {
                          key: "delete",
                          label: "Delete",
                          onClick: () => deleteTodo(item._id),
                        },
                      ]}
                    />
                  );
                  return (
                    <div key={index}>
                      {activeEdit === item?._id ? (
                        <>
                          <input
                            className="border-none outline-none bg-[#cabdbd8c] hover:border-none p-1 text-[12px] font-normal leading-[100%] tracking-normal w-[-webkit-fill-available]"
                            defaultValue={item.description}
                            onChange={handleEditChange}
                            onKeyDown={handleEditKeyDown}
                          />
                        </>
                      ) : (
                        <>
                          <div className="flex justify-between">
                            <Checkbox
                              checked={item.completed}
                              onChange={(e) =>
                                updateTodo(
                                  item._id,
                                  e.target.checked,
                                  item.description
                                )
                              }
                            >
                              <p
                                className={`  text-[12px] font-normal leading-[100%] tracking-normal break-all font-['Charter'] ${
                                  item.completed
                                    ? "text-[#A2A2A2] line-through"
                                    : "text-black"
                                }`}
                              >
                                {item.description}
                              </p>
                            </Checkbox>
                            <Dropdown
                              menu={{ items: menu.props.items }}
                              trigger={["click"]}
                              getPopupContainer={(triggerNode) =>
                                triggerNode.parentElement as HTMLElement
                              }
                            >
                              <MoreOutlined className="cursor-pointer" />
                            </Dropdown>
                          </div>
                        </>
                      )}
                    </div>
                  );
                }
              )}
            </>
          )}
        </div>
        {isChecked ? (
          <>
            <input
              className="border-none outline-none bg-[#D9D9D9]/10 hover:border-none p-1 text-[12px] font-normal leading-[100%] tracking-normal m-6  w-[-webkit-fill-available] text-white"
              value={inputAiValue}
              onChange={handleAiChange}
              onKeyDown={handleAiKeyDown}
            />
          </>
        ) : (
          <>
            <input
              className="border-none outline-none bg-[rgba(27,27,27,0.1)] hover:border-none p-1 text-[12px] font-normal leading-[100%] tracking-normal m-6  w-[-webkit-fill-available] text-black"
              value={inputValue}
              onChange={handleChange}
              onKeyDown={handleKeyDown}
            />
          </>
        )}
      </motion.div>

      {/* Overlay for mobile */}
      {isMobile && (
        <div
          className="fixed inset-0 bg-black/50 z-[9998]"
          onClick={() => setOpenTodoPop(false)}
        />
      )}
    </>
  );
};

export default TodoPopover;

// import todoIcon from "../../assets/images/todoIcon.svg";
// import { useState, useRef, useEffect } from "react";
// import TodoPopover from "./TodoPopover";
// import { AnimatePresence } from "framer-motion";

const Heading = () => {
  // const [openTodoPop, setOpenTodoPop] = useState(false);
  // const popoverRef = useRef<HTMLDivElement>(null);

  // useEffect(() => {
  //   const handleClickOutside = (event: MouseEvent) => {
  //     if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
  //       setOpenTodoPop(false);
  //     }
  //   };

  //   document.addEventListener('mousedown', handleClickOutside);
  //   return () => document.removeEventListener('mousedown', handleClickOutside);
  // }, []);

  return (
    <div className="flex justify-end mt-14 mx-10 sm:mt-14">
      {/* <div className="relative" ref={popoverRef}>
        <div
          className="bg-white p-3.5 rounded-[50%] cursor-pointer"
          onClick={() => setOpenTodoPop(!openTodoPop)}
        >
          <img
            src={todoIcon}
            alt="newsIcon"
            loading="lazy"
            height={14}
            width={16}
          />
        </div>
        <AnimatePresence>
          {openTodoPop && (
            <>
              <TodoPopover setOpenTodoPop={setOpenTodoPop} />
            </>
          )}
        </AnimatePresence>
      </div> */}
    </div>
  );
};

export default Heading;

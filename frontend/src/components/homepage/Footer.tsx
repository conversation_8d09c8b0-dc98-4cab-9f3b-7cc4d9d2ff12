import settingsIcon from "../../assets/images/settingsIcon.svg";
import { AnimatePresence, motion } from "framer-motion";
import { useState, useEffect, useMemo } from "react";
import { logOut } from "../../services/authService";
import Loader from "../common/Loader";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import AccountModal from "./account/AccountModal";
import { useQuotes } from "../../hooks/useQuotes";

const Footer = () => {
  const navigate = useNavigate();
  const [openSettingMenu, setOpenSettingMenu] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [location, setLocation] = useState<string>("Loading...");
  const [currentDate, setCurrentDate] = useState<string>("");
  const [showAccountModal, setShowAccountModal] = useState(false);
  const { quotes, refetch } = useQuotes();
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 767);
  const randomQuote = useMemo(
    () => quotes[Math.floor(Math.random() * quotes.length)],
    [quotes]
  );

  useEffect(() => {
    const date = new Date();
    setCurrentDate(
      date.toLocaleDateString("en-US", { month: "2-digit", year: "2-digit" })
    );

    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            const response = await axios.get(
              `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${position.coords.latitude}&longitude=${position.coords.longitude}`
            );
            const data = response.data;
            setLocation(`${data.city}, ${data.principalSubdivision}`);
          } catch (error) {
            console.log("error", error);
            setLocation("Location unavailable");
          }
        },
        () => {
          setLocation("Location unavailable");
        }
      );
    }
    refetch();
  }, [refetch]);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handleSignOut = () => {
    setIsLoading(true);
    logOut()
      .then(() => {
        localStorage.removeItem("authToken");
      })
      .catch((error) => {
        console.error("User fetch error:", error);
      })
      .finally(() => {
        setIsLoading(false);
        navigate("/");
      });
  };

  return (
    <>
      <div className="flex justify-between flex-col-reverse md:flex-row mb-8 px-12 gap-4">
        <div className="flex gap-3 justify-center">
          <div className="border-2 rounded-[25px] text-white flex items-center">
            <h1 className="font-medium text-[14px] sm:text-[17px] leading-[100%] tracking-[0%] text-center px-4 py-2.5 sm:px-6">
              {location}
            </h1>
          </div>
          <div className="border-2 border-white rounded-[25px] bg-white flex items-center">
            <h1 className="font-medium text-[14px] sm:text-[17px] leading-[100%] tracking-[0%] text-center px-4 py-2.5 sm:px-6">
              {currentDate}
            </h1>
          </div>
        </div>

        <div className="flex items-center justify-center">
          <p className="  font-bold text-[14px] sm:text-[18px] leading-[100%] tracking-[0%] text-white text-center">
            {quotes.length > 0
              ? `${randomQuote?.quote} - ${randomQuote?.author}`
              : ""}
          </p>
        </div>

        <div className="flex gap-2 ps-0 lg:ps-[12%] justify-center">
          <div className="relative">
            <motion.img
              src={settingsIcon}
              alt="settingsIcon"
              loading="lazy"
              height={34}
              width={34}
              className="cursor-pointer"
              animate={{
                rotate: openSettingMenu ? 225 : 0,
              }}
              transition={{ duration: 0.5, ease: "easeInOut" }}
              onClick={() => {
                setOpenSettingMenu(!openSettingMenu);
              }}
            />
            <AnimatePresence>
              {openSettingMenu && (
                <>
                  <motion.div
                    initial={{ opacity: 0, y: 0 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 0 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                    className="absolute bottom-12 md:bottom-16 -right-[115%] md:right-[50%] items-center flex flex-col gap-2 w-max bg-[#FFFFFF8C] rounded-[15px] p-3 z-[9999]"
                  >
                    <button
                      className="font-semibold text-[10px] leading-[100%] tracking-[0%] w-[-webkit-fill-available] px-7 py-2.5 text-center text-[#282828] bg-[#FFFFFF] rounded-[25px] cursor-pointer"
                      onClick={() => {
                        setShowAccountModal(true);
                        setOpenSettingMenu(false);
                      }}
                    >
                      Account
                    </button>
                    <button
                      className="  font-semibold text-[10px] leading-[100%] tracking-[0%] w-[-webkit-fill-available] px-4 py-2.5 text-center text-[#282828] bg-[#FFFFFF] rounded-[25px] cursor-pointer"
                      onClick={handleSignOut}
                    >
                      Sign out
                    </button>
                  </motion.div>
                </>
              )}
            </AnimatePresence>
          </div>
        </div>
        <Loader loading={isLoading} />
      </div>

      <AnimatePresence>
        {showAccountModal && (
          <AccountModal setShowAccountModal={setShowAccountModal} />
        )}
      </AnimatePresence>

      {isMobile && openSettingMenu && (
        <div
          className="fixed inset-0 bg-black/50 z-[9998]"
          onClick={() => setOpenSettingMenu(false)}
        />
      )}
    </>
  );
};

export default Footer;

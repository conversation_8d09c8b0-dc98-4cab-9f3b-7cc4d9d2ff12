import ArrowIcon from "../../assets/images/arrowIcon.svg";
import { motion } from "framer-motion";

type Props = {
  isChecked: boolean;
  setIsChecked: (value: boolean) => void;
};

const SwitchButton = ({ isChecked, setIsChecked }: Props) => {
  return (
    <div
      className={`w-[159px] p-[12px] ${
        isChecked ? "bg-[#ffffff]" : "bg-[#252525]"
      } rounded-full flex items-center cursor-pointer relative`}
      onClick={() => setIsChecked(!isChecked)}
    >
      <motion.div
        className="w-[40px] h-[22px] bg-white rounded-full flex items-center justify-center shadow-md absolute top-[1px] left-[1px] xl:left-[1.5px]"
        layout
        transition={{ duration:0.2 ,ease:'easeInOut' }}
        animate={{
          x: isChecked ? 117 : 0,
          backgroundColor: isChecked ? "#222222" : "#F2F2F2",
        }}
      >
        <span className={`text-sm`}>
          <img
            loading="lazy"
            className={`${isChecked ? "rotate-180 " : "invert"} py-1`}
            src={ArrowIcon}
            width={7}
            height={7}
            alt="ArrowIcon"
          />
        </span>
      </motion.div>
      <motion.span
        className="text-[#222222] text-[13px] font-normal leading-[100%] tracking-[0] absolute left-[30px]"
        initial={{ opacity: 0 }}
        animate={{ opacity: isChecked ? 1 : 0 }}
        transition={{ duration: 0.3 }}
      >
        To Do List
      </motion.span>
      <motion.span
        className="text-white text-[13px] font-normal leading-[100%] tracking-[0] absolute left-[54px]"
        initial={{ opacity: 0 }}
        animate={{ opacity: isChecked ? 0 : 1 }}
        transition={{ duration: 0.3 }}
      >
        Mergen Tasks
      </motion.span>
    </div>
  );
};

export default SwitchButton;

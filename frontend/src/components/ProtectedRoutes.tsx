import { ReactNode } from "react";
import { Navigate } from "react-router-dom";
import { ImageProvider } from "../context/ImageContext";
import { QuoteProvider } from "../context/QuoteContext";
import { GoalProvider } from "../context/GoalContext";

const ProtectedRoutes = ({ children }: { children: ReactNode }) => {
  const isAuthenticated = !!localStorage.getItem("authToken");
  return isAuthenticated ? (
    <ImageProvider>
      <QuoteProvider>
        <GoalProvider>{children}</GoalProvider>
      </QuoteProvider>
    </ImageProvider>
  ) : (
    <Navigate to="/" />
  );
};

export default ProtectedRoutes;

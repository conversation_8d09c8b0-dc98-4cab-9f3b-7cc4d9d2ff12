import { useState, ReactNode } from "react";
import { LoaderContext } from "../hooks/useLoader";

interface LoaderContextType {
  loading: boolean;
  setLoading: (value: boolean) => void;
}

export const LoaderProvider = ({ children }: { children: ReactNode }) => {
  const [loading, setLoading] = useState(false);

  return (
    <LoaderContext.Provider value={{ loading, setLoading }}>
      {children}
    </LoaderContext.Provider>
  );
};

export type { LoaderContextType };

// context/QuoteContext.tsx
import React, { useCallback, useState } from "react";
import { getGoals } from "../services/accountService";
import { GoalContext } from "../hooks/useGoal";

export interface Goal {
  _id: string;
  title: string;
  description: string;
}
interface GoalContextType {
  goals: Goal[];
  refetch: () => Promise<void>;
}

export const GoalProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [goals, setGoals] = useState<Goal[]>([]);

  const fetchGoals = useCallback(async () => {
    try {
      const response = await getGoals();
      setGoals(response.data);
    } catch (error: unknown) {
      console.log("error", error);
    }
  }, []);

  return (
    <GoalContext.Provider value={{ goals, refetch: fetchGoals }}>
      {children}
    </GoalContext.Provider>
  );
};

export type { GoalContextType };

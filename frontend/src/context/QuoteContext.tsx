// context/QuoteContext.tsx
import React, { useCallback, useState } from "react";
import { getQuotes } from "../services/accountService";
import { QuoteContext } from "../hooks/useQuotes";

export interface Quote {
  _id: string;
  author: string;
  quote: string;
}
interface QuoteContextType {
  quotes: Quote[];
  refetch: () => Promise<void>;
}

export const QuoteProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [quotes, setQuotes] = useState<Quote[]>([]);

  const fetchQuotes = useCallback(async () => {
    try {
      const response = await getQuotes();
      setQuotes(response.data);
    } catch (error: unknown) {
      console.log("error", error);
    }
  }, []);

  return (
    <QuoteContext.Provider value={{ quotes, refetch: fetchQuotes }}>
      {children}
    </QuoteContext.Provider>
  );
};

export type { QuoteContextType };

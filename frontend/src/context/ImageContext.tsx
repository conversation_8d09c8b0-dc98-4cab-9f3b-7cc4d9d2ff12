// context/ImageContext.tsx
import React, {
  useCallback,
  useState,
} from "react";
import { getImages } from "../services/adminService";
import { ImageContext } from "../hooks/useImages";

type ImageType = {
  id: string;
  url: string;
  description: string;
}[];

interface ImageContextType {
  imageUrls: ImageType;
  refetch: () => Promise<void>;
}


export const ImageProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [imageUrls, setImageUrls] = useState<ImageType>([]);

  const fetchImages = useCallback(async () => {
    try {
      const res = await getImages();
      setImageUrls(res);
    } catch (err) {
      console.error("Failed to fetch images", err);
    }
  }, []);

  return (
    <ImageContext.Provider value={{ imageUrls, refetch: fetchImages }}>
      {children}
    </ImageContext.Provider>
  );
};

export type { ImageContextType };

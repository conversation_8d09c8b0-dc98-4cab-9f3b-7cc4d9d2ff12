import { useCallback, useState } from "react";
import { ReactNode } from "react";
import { UserContext } from "../hooks/useUser";

interface UserContextType {
  user: {
    name: string;
    email: string;
    token: string;
    firstName: string;
    lastName: string;
    username: string;
  } | null;
  setUser: (
    user: {
      name: string;
      email: string;
      token: string;
      firstName: string;
      lastName: string;
      username: string;
    } | null
  ) => void;
  loginUserDetails: (userData: {
    name: string;
    email: string;
    token: string;
    firstName: string;
    lastName: string;
    username: string;
  }) => void;
  logout: () => void;
  loadUserFromStorage: () => void;
}

export const UserProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<{
    name: string;
    email: string;
    token: string;
    firstName: string;
    lastName: string;
    username: string;
  } | null>(null); // user = { name, email, token, etc. }

  const loginUserDetails = (userData: {
    name: string;
    email: string;
    token: string;
    firstName: string;
    lastName: string;
    username: string;
  }) => {
    setUser(userData);
    localStorage.setItem("user", JSON.stringify(userData));
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem("user");
  };

  const loadUserFromStorage = useCallback(() => {
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
  }, []);

  return (
    <UserContext.Provider
      value={{ user, setUser, loginUserDetails, logout, loadUserFromStorage }}
    >
      {children}
    </UserContext.Provider>
  );
};

export type { UserContextType };

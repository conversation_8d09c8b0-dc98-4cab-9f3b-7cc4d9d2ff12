@import "tailwindcss";

@font-face {
    font-family: 'Inter';
    src: url('./assets/fonts/Inter/Inter-VariableFont_opsz,wght.ttf') format('truetype');
    font-weight: 100 900;
    font-style: normal;
}

@font-face {
    font-family: 'Charter';
    src: url('./assets/fonts/charter/ttf/Charter Regular.ttf') format('truetype');
    font-weight: 100 900;
    font-style: normal;
}

@font-face {
    font-family: 'Tw-cen-mt';
    src: url('./assets/fonts/Tw-Cen-Mt/Tw-Cen-MT-Condensed-Font.ttf') format('truetype');
    font-weight: 100 900;
    font-style: normal;
}

.listCardContainer,
.bg-drawer,
.report-section,
.home-section,
.ant-flex,
.ant-form-item-control-input-content,.font-Inter {
    font-family: 'Inter' !important;
}

.footer-Section,
.todo-container {
    font-family: 'Charter';
}

.ant-popover-inner {
    background: transparent !important;
    box-shadow: none !important;
}

.ant-popover-arrow {
    display: none !important;
}

.ant-tooltip-inner {
    background-color: white !important;
    padding: 0 !important;
}

.ant-tooltip-arrow::after {
    background: white !important;
}

.ant-checkbox-checked .ant-checkbox-inner {
    background-color: black !important;
    border-color: black !important;
}

.cardContainer {
    height: calc(100vh - 400px);
}

.listCardContainer {
    height: calc(100vh - 146px);
}

.bg-drawer {
    background-image: url("./assets/images/historyDrawerBg.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.historyList {
    height: calc(100vh - 270px);
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;
}

.scrollbar-custom::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.scrollbar-custom::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
}

.listViewContainer {
    width: calc(100% - 279px);
}

.truncate-text {
    display: -webkit-box;
    -webkit-line-clamp: 8;
    line-clamp: 8;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
}

.truncate-title {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
}

.truncate-item {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
}

.bg-todolist {
    background-image: url("./assets/images/TodoBackground.png");
    background-position: center;
    background-repeat: no-repeat;
}

.textarea-hide-scrollbar {
    overflow: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.textarea-hide-scrollbar::-webkit-scrollbar {
    display: none;
}

@media print {
    .no-print {
        display: none !important;
    }
}

@page {
    margin: 2cm;
}

@media (max-width: 640px) {
    .cardConatiner {
        height: calc(100vh - 350px);
    }
}


@media (max-width: 1023px) {
    .listViewContainer {
        width: 100%;
    }
}
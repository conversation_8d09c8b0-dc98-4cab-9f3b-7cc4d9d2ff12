import { Route, Routes } from "react-router-dom";
import HomePage from "./components/homepage/HomePage";
import Dashboard from "./components/dashboard/Dashboard";
import SignUp from "./components/auth/SignUp";
import LogIn from "./components/auth/LogIn";
import ForgetPassword from "./components/auth/ForgetPassword";
import ResetPassword from "./components/auth/ResetPassword";
import ProtectedRoutes from "./components/ProtectedRoutes";
import Loader from "./components/common/Loader";
import { useEffect } from "react";
import { useLoader } from "./hooks/useLoader";
import { useUser } from "./hooks/useUser";

function App() {
  const { loading } = useLoader();
  const { loadUserFromStorage } = useUser();
  useEffect(() => {
    loadUserFromStorage();
  }, [loadUserFromStorage]);
  return (
    <>
      <Routes>
        <Route path="/" element={<LogIn />} />
        <Route path="/signUp" element={<SignUp />} />
        <Route path="/forgetPassword" element={<ForgetPassword />} />
        <Route path="/resetPassword/:token" element={<ResetPassword />} />
        <Route
          path="/search"
          element={<ProtectedRoutes children={<HomePage />} />}
        />
        <Route
          path="/dashboard/*"
          element={<ProtectedRoutes children={<Dashboard />} />}
        />
      </Routes>
      <Loader loading={loading} />
    </>
  );
}

export default App;

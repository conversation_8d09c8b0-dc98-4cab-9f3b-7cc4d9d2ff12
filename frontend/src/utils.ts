export const formatMonth = (dateString: string) => {
  const date = new Date(dateString);
  const monthName = date.toLocaleString("en-US", { month: "long" });
  return monthName;
};

export const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  let day: string | number = date.getUTCDate();
  day = day < 10 ? `0${day}` : `${day}`;
  return day;
};

export const formatReferenceDate=(dateString: string)=>{
  const date = new Date(dateString);
const year = date.getUTCFullYear();
const day = String(date.getUTCDate()).padStart(2, '0');
const month = date.toLocaleString('en-US', { month: 'short', timeZone: 'UTC' }).toUpperCase();
return {year,day,month}
}

export const timeAgo=(dateString:string)=> {
  const now = new Date();
  const past = new Date(dateString);
  const secondsAgo = Math.floor((now.getTime() - past.getTime()) / 1000);

  const intervals = [
    { label: 'year', seconds: 365 * 24 * 60 * 60 },
    { label: 'month', seconds: 30 * 24 * 60 * 60 },
    { label: 'day', seconds: 24 * 60 * 60 },
    { label: 'hour', seconds: 60 * 60 },
    { label: 'minute', seconds: 60 },
    { label: 'second', seconds: 1 },
  ];

  for (const interval of intervals) {
    const count = Math.floor(secondsAgo / interval.seconds);
    if (count >= 1) {
      return `${count} ${interval.label}${count > 1 ? 's' : ''} ago`;
    }
  }

  return 'just now';
}

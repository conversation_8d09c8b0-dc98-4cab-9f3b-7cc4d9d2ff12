{"name": "agent-onboarding", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@tailwindcss/vite": "^4.0.14", "antd": "^5.24.4", "axios": "^1.8.4", "framer-motion": "^12.5.0", "quill": "^2.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-quill": "^2.0.0", "react-router-dom": "^7.3.0", "react-spinners": "^0.15.0", "react-to-print": "^3.0.5", "tailwindcss": "^4.0.14"}, "devDependencies": {"@eslint/js": "^9.21.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jsdom": "^21.1.7", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "jsdom": "^26.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vite-imagetools": "^7.0.5", "vite-plugin-compression": "^0.5.1", "vitest": "^3.1.1"}}
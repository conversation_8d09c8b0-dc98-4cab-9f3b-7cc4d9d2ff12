/// <reference types="vitest" />
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";
import compression from "vite-plugin-compression";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    tailwindcss(),
    compression(),
  ],
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: "./src/setupTests.ts",
  },
  server: {
    host: true,
    port: 5173,
    allowedHosts: ['chat.mergenai.io'],
  },
  preview: {
    host: true,
    port: 4173,
    allowedHosts: ['chat.mergenai.io'],
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          react: ["react", "react-dom"],
          antd: ["antd"],
          quill: ["react-quill", "quill"],
          motion: ["framer-motion"],
        },
      },
    },
  },
});

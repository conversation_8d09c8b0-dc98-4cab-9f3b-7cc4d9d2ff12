import {
  CreateConversationRequest,
  ConversationResponse,
  UpdateConversationRequest,
  ApiResponse,
} from '../types/api.types';
import { ProtectedAPI } from './messageService';

/**
 * Create a new conversation
 */
export const createConversation = async (
  payload: CreateConversationRequest
): Promise<ApiResponse<ConversationResponse>> => {
  const response = await ProtectedAPI.post('/conversations', payload);
  return response.data;
};

/**
 * Get all conversations for the current user
 */
export const getConversation = async (params?: {
  isDeepResearch?: boolean;
  page?: number;
  limit?: number;
}): Promise<ApiResponse<ConversationResponse[]>> => {
  const response = await ProtectedAPI.get('/conversations', { params });
  return response.data;
};

/**
 * Get a specific conversation by ID
 */
export const getConversationById = async (
  conversationId: string
): Promise<ApiResponse<ConversationResponse>> => {
  const response = await ProtectedAPI.get(`/conversations/${conversationId}`);
  return response.data;
};

/**
 * Update a conversation
 */
export const updateConversation = async (
  conversationId: string,
  updates: UpdateConversationRequest
): Promise<ApiResponse<ConversationResponse>> => {
  const response = await ProtectedAPI.patch(`/conversations/${conversationId}`, updates);
  return response.data;
};

/**
 * Delete a conversation
 */
export const deleteConversation = async (
  conversationId: string
): Promise<ApiResponse<void>> => {
  const response = await ProtectedAPI.delete(`/conversations/${conversationId}`);
  return response.data;
};

/**
 * Search conversations by title
 */
export const searchConversations = async (
  query: string
): Promise<ApiResponse<ConversationResponse[]>> => {
  const response = await ProtectedAPI.get('/conversations/search', {
    params: { q: query }
  });
  return response.data;
};

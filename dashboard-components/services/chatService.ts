import {
  CreateChatRequest,
  ChatResponse,
  ApiResponse,
} from '../types/api.types';
import { ProtectedAPI } from './messageService';

/**
 * Create a new chat
 */
export const createChat = async (
  payload: CreateChatRequest
): Promise<ApiResponse<ChatResponse>> => {
  const response = await ProtectedAPI.post('/chats', payload);
  return response.data;
};

/**
 * Get chat by conversation ID
 */
export const getChatByConversationId = async (
  conversationId: string
): Promise<ApiResponse<ChatResponse[]>> => {
  const response = await ProtectedAPI.get(`/chats/conversation/${conversationId}`);
  return response.data;
};

/**
 * Get a specific chat by ID
 */
export const getChatById = async (
  chatId: string
): Promise<ApiResponse<ChatResponse>> => {
  const response = await ProtectedAPI.get(`/chats/${chatId}`);
  return response.data;
};

/**
 * Update a chat
 */
export const updateChat = async (
  chatId: string,
  updates: Partial<CreateChatRequest>
): Promise<ApiResponse<ChatResponse>> => {
  const response = await ProtectedAPI.patch(`/chats/${chatId}`, updates);
  return response.data;
};

/**
 * Delete a chat
 */
export const deleteChat = async (
  chatId: string
): Promise<ApiResponse<void>> => {
  const response = await ProtectedAPI.delete(`/chats/${chatId}`);
  return response.data;
};

/**
 * Get all chats with pagination
 */
export const getChats = async (params?: {
  page?: number;
  limit?: number;
  conversationId?: string;
}): Promise<ApiResponse<ChatResponse[]>> => {
  const response = await ProtectedAPI.get('/chats', { params });
  return response.data;
};

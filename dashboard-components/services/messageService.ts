import axios, { AxiosInstance } from 'axios';
import {
  CreateMessageRequest,
  CreateMessageResponse,
  GetMessagesResponse,
  ApiResponse,
} from '../types/api.types';

// Base URL - replace with your actual API URL
const BASE_URL = process.env.REACT_APP_API_URL || 'https://backend.mergenai.io/api/v1';

// Create axios instance with interceptors
export const ProtectedAPI: AxiosInstance = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
ProtectedAPI.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
ProtectedAPI.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      window.location.href = '/';
    }
    return Promise.reject(error);
  }
);

/**
 * Create a new message
 */
export const createMessage = async (payload: CreateMessageRequest): Promise<ApiResponse<CreateMessageResponse>> => {
  const formData = new FormData();
  
  // Append basic fields
  formData.append('chatId', payload.chatId);
  formData.append('content', payload.content);
  formData.append('sender', payload.sender);
  formData.append('isDeepResearch', JSON.stringify(payload.isDeepResearch || false));

  // Append files if they exist
  if (payload.attachedFiles) {
    payload.attachedFiles.image?.forEach((file) => {
      formData.append('image[]', file);
    });
    
    payload.attachedFiles.audio?.forEach((file) => {
      formData.append('audio[]', file);
    });
    
    payload.attachedFiles.url?.forEach((url) => {
      formData.append('url[]', url);
    });
    
    payload.attachedFiles.file?.forEach((file) => {
      formData.append('file[]', file);
    });
  }

  const response = await ProtectedAPI.post('/messages', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  
  return response.data;
};

/**
 * Get messages by chat ID
 */
export const getMessageByChatId = async (chatId: string): Promise<ApiResponse<GetMessagesResponse[]>> => {
  const response = await ProtectedAPI.get(`/messages/chat/${chatId}`);
  return response.data;
};

/**
 * Get a specific message by ID
 */
export const getMessageById = async (messageId: string): Promise<ApiResponse<GetMessagesResponse>> => {
  const response = await ProtectedAPI.get(`/messages/${messageId}`);
  return response.data;
};

/**
 * Update a message
 */
export const updateMessage = async (
  messageId: string, 
  updates: Partial<CreateMessageRequest>
): Promise<ApiResponse<GetMessagesResponse>> => {
  const response = await ProtectedAPI.patch(`/messages/${messageId}`, updates);
  return response.data;
};

/**
 * Delete a message
 */
export const deleteMessage = async (messageId: string): Promise<ApiResponse<void>> => {
  const response = await ProtectedAPI.delete(`/messages/${messageId}`);
  return response.data;
};

/**
 * Get messages with pagination
 */
export const getMessages = async (params?: {
  page?: number;
  limit?: number;
  chatId?: string;
}): Promise<ApiResponse<GetMessagesResponse[]>> => {
  const response = await ProtectedAPI.get('/messages', { params });
  return response.data;
};

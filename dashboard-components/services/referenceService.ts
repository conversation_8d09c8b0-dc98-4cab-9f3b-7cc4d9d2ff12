import {
  CreateReferenceRequest,
  ReferenceResponse,
  ApiResponse,
} from '../types/api.types';
import { ProtectedAPI } from './messageService';

/**
 * Create a new reference
 */
export const createReference = async (
  payload: CreateReferenceRequest
): Promise<ApiResponse<ReferenceResponse>> => {
  const response = await ProtectedAPI.post('/references', payload);
  return response.data;
};

/**
 * Get references by message ID
 */
export const getReferencesByMessageId = async (
  messageId: string
): Promise<ReferenceResponse[]> => {
  const response = await ProtectedAPI.get(`/references/message/${messageId}`);
  return response.data;
};

/**
 * Get a specific reference by ID
 */
export const getReferenceById = async (
  referenceId: string
): Promise<ApiResponse<ReferenceResponse>> => {
  const response = await ProtectedAPI.get(`/references/${referenceId}`);
  return response.data;
};

/**
 * Update a reference
 */
export const updateReference = async (
  referenceId: string,
  updates: Partial<CreateReferenceRequest>
): Promise<ApiResponse<ReferenceResponse>> => {
  const response = await ProtectedAPI.patch(`/references/${referenceId}`, updates);
  return response.data;
};

/**
 * Delete a reference
 */
export const deleteReference = async (
  referenceId: string
): Promise<ApiResponse<void>> => {
  const response = await ProtectedAPI.delete(`/references/${referenceId}`);
  return response.data;
};

/**
 * Get all references with pagination
 */
export const getReferences = async (params?: {
  page?: number;
  limit?: number;
  messageId?: string;
}): Promise<ApiResponse<ReferenceResponse[]>> => {
  const response = await ProtectedAPI.get('/references', { params });
  return response.data;
};

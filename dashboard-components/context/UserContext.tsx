import React, { createContext, useState, useCallback, ReactNode } from 'react';
import { User, UserContextType } from '../types/dashboard.types';

// Create the context
export const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

/**
 * User Context Provider
 * Manages user authentication state and localStorage persistence
 */
export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);

  /**
   * Load user data from localStorage
   */
  const loadUserFromStorage = useCallback(() => {
    try {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        const userData = JSON.parse(storedUser);
        setUser(userData);
      }
    } catch (error) {
      console.error('Error loading user from storage:', error);
      // Clear invalid data
      localStorage.removeItem('user');
    }
  }, []);

  /**
   * Set user and persist to localStorage
   */
  const setUserWithPersistence = useCallback((userData: User | null) => {
    setUser(userData);
    
    if (userData) {
      localStorage.setItem('user', JSON.stringify(userData));
    } else {
      localStorage.removeItem('user');
      localStorage.removeItem('authToken');
    }
  }, []);

  const contextValue: UserContextType = {
    user,
    setUser: setUserWithPersistence,
    loadUserFromStorage,
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

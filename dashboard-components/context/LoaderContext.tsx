import React, { createContext, useState, ReactNode } from 'react';
import { LoaderContextType } from '../types/dashboard.types';

// Create the context
export const LoaderContext = createContext<LoaderContextType | undefined>(undefined);

interface LoaderProviderProps {
  children: ReactNode;
}

/**
 * Loader Context Provider
 * Manages global loading state for the application
 */
export const LoaderProvider: React.FC<LoaderProviderProps> = ({ children }) => {
  const [loading, setLoading] = useState<boolean>(false);

  const contextValue: LoaderContextType = {
    loading,
    setLoading,
  };

  return (
    <LoaderContext.Provider value={contextValue}>
      {children}
    </LoaderContext.Provider>
  );
};

import { useContext } from 'react';
import { LoaderContext } from '../context/LoaderContext';
import { LoaderContextType } from '../types/dashboard.types';

/**
 * Custom hook to access loader context
 * Must be used within LoaderProvider
 */
const useLoader = (): LoaderContextType => {
  const context = useContext(LoaderContext);
  
  if (context === undefined) {
    throw new Error('useLoader must be used within a LoaderProvider');
  }
  
  return context;
};

export default useLoader;

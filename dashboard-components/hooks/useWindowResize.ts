import { useState, useLayoutEffect } from 'react';
import { WindowSize } from '../types/dashboard.types';

/**
 * Custom hook to track window resize events
 * Returns current screen width and height
 */
const useWindowResize = (): WindowSize => {
  const [size, setSize] = useState<WindowSize>({
    screenWidth: typeof window !== 'undefined' ? window.innerWidth : 1024,
    screenHeight: typeof window !== 'undefined' ? window.innerHeight : 768,
  });

  useLayoutEffect(() => {
    const updateSize = () => {
      setSize({
        screenWidth: window.innerWidth,
        screenHeight: window.innerHeight,
      });
    };

    // Set initial size
    updateSize();

    // Add event listener
    window.addEventListener('resize', updateSize);

    // Cleanup
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  return size;
};

export default useWindowResize;

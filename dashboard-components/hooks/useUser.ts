import { useContext } from 'react';
import { UserContext } from '../context/UserContext';
import { UserContextType } from '../types/dashboard.types';

/**
 * Custom hook to access user context
 * Must be used within UserProvider
 */
const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  
  return context;
};

export default useUser;

# Dashboard Components Collection

This folder contains all the essential components used by the Mergen AI dashboard page, extracted and organized for easy reuse and understanding.

## ✅ Extraction Complete

All dashboard components have been successfully extracted from the original codebase and organized into this collection. Each component includes proper TypeScript interfaces, styling, and documentation.

## Folder Structure

```
dashboard-components/
├── README.md                    # This file - usage guide
├── components/
│   ├── common/
│   │   └── SearchBar.tsx        # ✅ Reusable input component with auto-resize
│   ├── dashboard/
│   │   ├── Dashboard.tsx        # ✅ Main dashboard orchestrator
│   │   ├── HeaderSection.tsx    # ✅ Navigation and user menu
│   │   ├── FooterSection.tsx    # ✅ Chat interface container
│   │   ├── ListViewSection.tsx  # ✅ Results container
│   │   ├── ListCards.tsx        # ✅ Individual result cards
│   │   ├── HistoryDrawer.tsx    # ✅ Conversation sidebar
│   │   └── CardSectionArea.tsx  # ✅ Alternative masonry view
│   └── modals/
│       ├── EmailSection.tsx     # ✅ Email modal component
│       └── ReportSection.tsx    # ✅ Report generation modal
├── hooks/
│   ├── useUser.ts              # ✅ User context hook
│   ├── useLoader.ts            # ✅ Loading state hook
│   └── useWindowResize.ts      # ✅ Window resize hook
├── context/
│   ├── UserContext.tsx         # ✅ User state management
│   └── LoaderContext.tsx       # ✅ Loading state management
├── services/
│   ├── messageService.ts       # ✅ Message API calls
│   ├── conversationService.ts  # ✅ Conversation API calls
│   ├── referenceService.ts     # ✅ Reference API calls
│   └── chatService.ts          # ✅ Chat API calls
├── types/
│   ├── dashboard.types.ts      # ✅ Component and state types
│   └── api.types.ts           # ✅ API request/response types
└── styles/
    └── globals.css            # ✅ Global styles and utilities
    └── api.types.ts           # API response interfaces
```

## Quick Start

### 1. Installation

```bash
# Install required dependencies
npm install react react-dom typescript
npm install tailwindcss framer-motion antd axios
npm install @types/react @types/react-dom
```

### 2. Basic Setup

```typescript
// App.tsx
import { UserProvider } from './dashboard-components/context/UserContext';
import { LoaderProvider } from './dashboard-components/context/LoaderContext';
import Dashboard from './dashboard-components/components/dashboard/Dashboard';
import './dashboard-components/styles/globals.css';

function App() {
  return (
    <UserProvider>
      <LoaderProvider>
        <Dashboard isListView={true} />
      </LoaderProvider>
    </UserProvider>
  );
}
```

### 3. Tailwind Configuration

```javascript
// tailwind.config.js
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./dashboard-components/**/*.{js,jsx,ts,tsx}"
  ],
  theme: {
    extend: {
      fontFamily: {
        'inter': ['Inter', 'sans-serif'],
        'charter': ['Charter', 'serif'],
      },
      colors: {
        'primary-dark': '#1E1E1E',
        'secondary-dark': '#353535',
        'bg-primary': '#F6F6F6',
        'border-primary': '#939393',
      }
    }
  }
}
```

## Component Usage Guide

### SearchBar Component

**Purpose**: Reusable input component with auto-resize and file upload support

```typescript
import SearchBar from './components/common/SearchBar';

const MyComponent = () => {
  const [inputValue, setInputValue] = useState("");
  
  const handleSearch = () => {
    console.log("Search:", inputValue);
  };

  return (
    <SearchBar
      color="#1E1E1E"
      border={false}
      inputValue={inputValue}
      setInputValue={setInputValue}
      handleSearch={handleSearch}
      placeholder="Type your message..."
      resize={true}
      disable={false}
      onPaste={(e) => console.log("File pasted:", e)}
    />
  );
};
```

**Props:**
- `color`: Button background color
- `border`: Show input border
- `inputValue`: Current input value
- `setInputValue`: Input change handler
- `handleSearch`: Submit handler
- `placeholder`: Input placeholder text
- `resize`: Enable auto-resize (textarea mode)
- `disable`: Disable input
- `onPaste`: File paste handler (optional)

### Dashboard Component

**Purpose**: Main dashboard orchestrator with state management

```typescript
import Dashboard from './components/dashboard/Dashboard';

const App = () => {
  return (
    <div className="min-h-screen bg-white">
      <Dashboard isListView={true} />
    </div>
  );
};
```

**Props:**
- `isListView`: Toggle between list view and card masonry view

**Features:**
- Complete state management for conversations, messages, and references
- API integration for chat functionality
- Responsive layout with drawer navigation
- Message navigation (previous/next)

### ListCards Component

**Purpose**: Individual result card with expandable sections

```typescript
import ListCards from './components/dashboard/ListCards';

const ResultsContainer = ({ results }) => {
  return (
    <div className="space-y-4">
      {results.map((item, index) => (
        <ListCards
          key={index}
          data={{
            summary: item.summary,
            keywords: item.keywords,
            key_points: item.key_points
          }}
          setBackUpData={setBackUpData}
          referenceData={referenceData}
          backupData={backupData}
        />
      ))}
    </div>
  );
};
```

**Data Structure:**
```typescript
interface ListCardData {
  summary: string;
  keywords: string[];
  key_points: string[];
}
```

### HistoryDrawer Component

**Purpose**: Animated sidebar for conversation history

```typescript
import HistoryDrawer from './components/dashboard/HistoryDrawer';

const MyDashboard = () => {
  const [openHistoryView, setOpenHistoryView] = useState(false);
  
  return (
    <div className="flex">
      <AnimatePresence>
        {openHistoryView && (
          <motion.div
            initial={{ x: "-100%", width: 0, opacity: 0 }}
            animate={{ x: 0, width: "279px", opacity: 1 }}
            exit={{ x: "-100%", width: 0, opacity: 0 }}
            transition={{ duration: 0.6, ease: "easeInOut" }}
          >
            <HistoryDrawer
              setOpenHistoryView={setOpenHistoryView}
              allConversations={conversations}
              activeConversation={activeConversation}
              setActiveConversation={setActiveConversation}
              getAllConversations={fetchConversations}
              setOpenEmailSection={setOpenEmailSection}
              activeChat={activeChat}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
```

## Custom Hooks Usage

### useUser Hook

```typescript
import { useUser } from './hooks/useUser';

const MyComponent = () => {
  const { user, setUser, loadUserFromStorage } = useUser();
  
  useEffect(() => {
    loadUserFromStorage();
  }, []);
  
  return (
    <div>
      {user ? `Welcome ${user.name}` : 'Please login'}
    </div>
  );
};
```

### useLoader Hook

```typescript
import { useLoader } from './hooks/useLoader';

const MyComponent = () => {
  const { loading, setLoading } = useLoader();
  
  const handleAsyncOperation = async () => {
    setLoading(true);
    try {
      await someApiCall();
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div>
      {loading && <div>Loading...</div>}
      <button onClick={handleAsyncOperation}>
        Start Operation
      </button>
    </div>
  );
};
```

### useWindowResize Hook

```typescript
import { useWindowResize } from './hooks/useWindowResize';

const ResponsiveComponent = () => {
  const { screenWidth, screenHeight } = useWindowResize();
  
  return (
    <div>
      <p>Screen: {screenWidth} x {screenHeight}</p>
      {screenWidth > 1024 ? (
        <div>Desktop Layout</div>
      ) : (
        <div>Mobile Layout</div>
      )}
    </div>
  );
};
```

## Service Layer Usage

### Message Service

```typescript
import { createMessage, getMessageByChatId } from './services/messageService';

const sendMessage = async () => {
  try {
    const response = await createMessage({
      chatId: "chat-id",
      content: "Hello world",
      sender: "user",
      attachedFiles: {
        image: [],
        audio: [],
        url: [],
        file: []
      }
    });
    console.log("Message sent:", response);
  } catch (error) {
    console.error("Error:", error);
  }
};
```

### Conversation Service

```typescript
import { createConversation, getConversation } from './services/conversationService';

const startNewConversation = async () => {
  try {
    const conversation = await createConversation({
      title: "New Chat"
    });
    console.log("Conversation created:", conversation);
  } catch (error) {
    console.error("Error:", error);
  }
};
```

## Styling Guidelines

### Using Custom CSS Classes

```css
/* Text truncation */
.truncate-text {
  display: -webkit-box;
  -webkit-line-clamp: 8;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar */
.scrollbar-custom::-webkit-scrollbar {
  width: 6px;
}

/* Glass morphism effect */
.glass-effect {
  background: linear-gradient(168.12deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0.1) 98.85%);
  backdrop-filter: blur(40px);
}
```

### Responsive Design Patterns

```typescript
// Conditional rendering based on screen size
const { screenWidth } = useWindowResize();

return (
  <div className={`
    ${screenWidth > 1024 ? 'lg:flex lg:space-x-6' : 'space-y-4'}
    ${screenWidth > 768 ? 'md:px-8' : 'px-4'}
  `}>
    {/* Content */}
  </div>
);
```

## Animation Patterns

### Framer Motion Examples

```typescript
// Page transition
<motion.div
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  exit={{ opacity: 0, y: -20 }}
  transition={{ duration: 0.3 }}
>
  {/* Content */}
</motion.div>

// Drawer animation
<motion.div
  initial={{ x: "-100%" }}
  animate={{ x: 0 }}
  exit={{ x: "-100%" }}
  transition={{ duration: 0.6, ease: "easeInOut" }}
>
  {/* Drawer content */}
</motion.div>
```

## Best Practices

1. **State Management**: Use Context API for global state, local state for component-specific data
2. **Error Handling**: Always wrap API calls in try-catch blocks
3. **Performance**: Use React.memo for expensive components, useMemo for calculations
4. **Accessibility**: Include proper ARIA labels and keyboard navigation
5. **Responsive Design**: Mobile-first approach with progressive enhancement
6. **Type Safety**: Use TypeScript interfaces for all props and data structures

## Dependencies

```json
{
  "react": "^18.3.1",
  "react-dom": "^18.3.1",
  "typescript": "latest",
  "tailwindcss": "^4.0.14",
  "framer-motion": "^12.5.0",
  "antd": "^5.24.4",
  "axios": "^1.8.4",
  "react-router-dom": "^7.3.0"
}
```

This component collection provides a complete foundation for building modern chat-based dashboard interfaces with React, TypeScript, and Tailwind CSS.

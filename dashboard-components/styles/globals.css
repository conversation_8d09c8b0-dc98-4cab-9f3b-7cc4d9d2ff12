/* Import Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Font Faces */
@font-face {
  font-family: 'Inter';
  src: url('../assets/fonts/Inter/Inter-VariableFont_opsz,wght.ttf') format('truetype');
  font-weight: 100 900;
  font-style: normal;
}

@font-face {
  font-family: 'Charter';
  src: url('../assets/fonts/charter/ttf/Charter Regular.ttf') format('truetype');
  font-weight: 100 900;
  font-style: normal;
}

@font-face {
  font-family: 'Tw-Cen-MT';
  src: url('../assets/fonts/tw-cen-mt/TCMT.ttf') format('truetype');
  font-weight: 100 900;
  font-style: normal;
}

/* CSS Variables for Colors */
:root {
  /* Primary Colors */
  --color-primary-dark: #1E1E1E;
  --color-secondary-dark: #353535;
  --color-tertiary-dark: #414141;
  --color-text-primary: #222222;
  
  /* Background Colors */
  --color-bg-primary: #FFFFFF;
  --color-bg-secondary: #F6F6F6;
  --color-bg-tertiary: #ECECEC;
  
  /* Border Colors */
  --color-border-primary: #939393;
  --color-border-secondary: #9E9E9E;
  
  /* Dynamic Card Colors */
  --color-card-yellow: #F8DE7E;
  --color-card-peach: #E8AE77;
  --color-card-gray: #D3D3D3;
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
}

/* Text Truncation Utilities */
.truncate-text {
  display: -webkit-box;
  -webkit-line-clamp: 8;
  line-clamp: 8;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.truncate-item {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

/* Scrollbar Customization */
.scrollbar-custom {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

.scrollbar-custom::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-custom::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.5);
}

/* Hide Scrollbar */
.scrollbar-hide {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Textarea Hide Scrollbar */
.textarea-hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.textarea-hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Glass Morphism Effects */
.glass-effect {
  background: linear-gradient(168.12deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.1) 98.85%);
  backdrop-filter: blur(40px);
  box-shadow: 0px 4px 24px -1px rgba(136, 136, 136, 0.12);
}

.bg-drawer {
  background-image: url('../assets/images/historyDrawerBg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* Container Heights */
.container-card {
  height: calc(100vh - 400px);
}

.container-list {
  height: calc(100vh - 146px);
}

.container-history {
  height: calc(100vh - 270px);
}

.listCardContainer {
  height: calc(100vh - 146px);
}

.cardContainer {
  height: calc(100vh - 400px);
}

.historyList {
  height: calc(100vh - 270px);
}

/* Footer Section */
.footer-Section {
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* Responsive Grid */
.grid-responsive {
  columns: 1;
  gap: 0;
}

@media (min-width: 768px) {
  .grid-responsive {
    columns: 2;
  }
}

@media (min-width: 1024px) {
  .grid-responsive {
    columns: 3;
  }
}

@media (min-width: 1280px) {
  .grid-responsive {
    columns: 4;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in-left {
  animation: slideInLeft 0.6s ease-in-out;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Focus States */
.focus-ring:focus {
  outline: 2px solid var(--color-primary-dark);
  outline-offset: 2px;
}

/* Button Hover Effects */
.btn-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease-in-out;
}

/* Card Hover Effects */
.card-hover:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease-in-out;
}

/* Loading Spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid var(--color-primary-dark);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
}

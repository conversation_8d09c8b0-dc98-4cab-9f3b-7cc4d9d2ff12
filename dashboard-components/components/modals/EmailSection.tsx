import { useState } from "react";
import { motion } from "framer-motion";
import { EmailSectionProps } from "../../types/dashboard.types";

const EmailSection: React.FC<EmailSectionProps> = ({
  openEmailSection,
  setOpenEmailSection,
  activeChat,
}) => {
  const [emailData, setEmailData] = useState({
    to: "",
    subject: "",
    message: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setEmailData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSendEmail = async () => {
    if (!emailData.to || !emailData.subject || !emailData.message) {
      alert("Please fill in all fields");
      return;
    }

    setIsLoading(true);
    
    try {
      // Implement your email sending logic here
      // Example: await sendEmail({ ...emailData, chatId: activeChat });
      
      // For now, we'll use mailto as a fallback
      const mailtoLink = `mailto:${emailData.to}?subject=${encodeURIComponent(emailData.subject)}&body=${encodeURIComponent(emailData.message)}`;
      window.location.href = mailtoLink;
      
      setOpenEmailSection(false);
      setEmailData({ to: "", subject: "", message: "" });
    } catch (error) {
      console.error("Error sending email:", error);
      alert("Failed to send email. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (!openEmailSection) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-500 flex items-center justify-center p-4 sm:p-0"
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        transition={{ duration: 0.2 }}
        className="bg-white/5 backdrop-blur-xl w-[95%] sm:w-[85%] lg:w-[60%] shadow-lg flex flex-col sm:flex-row rounded-xl overflow-hidden"
      >
        {/* Left Panel - Email Form */}
        <div className="w-full p-4 sm:px-12 sm:pt-12 bg-black/70 backdrop-blur-md border-b sm:border-b-0 sm:border-r border-white/10">
          <div className="flex flex-col gap-4 h-full">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-white text-xl font-bold">Send Email</h2>
              <button
                onClick={() => setOpenEmailSection(false)}
                className="text-white hover:text-gray-300 text-2xl"
              >
                ×
              </button>
            </div>

            {/* Email Form */}
            <div className="space-y-4 flex-1">
              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  To:
                </label>
                <input
                  type="email"
                  value={emailData.to}
                  onChange={(e) => handleInputChange("to", e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  Subject:
                </label>
                <input
                  type="text"
                  value={emailData.subject}
                  onChange={(e) => handleInputChange("subject", e.target.value)}
                  placeholder="Email subject"
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="flex-1">
                <label className="block text-white text-sm font-medium mb-2">
                  Message:
                </label>
                <textarea
                  value={emailData.message}
                  onChange={(e) => handleInputChange("message", e.target.value)}
                  placeholder="Your message here..."
                  rows={8}
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <button
                onClick={() => setOpenEmailSection(false)}
                className="flex-1 px-6 py-3 bg-white/10 border border-white/20 text-white rounded-lg hover:bg-white/20 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSendEmail}
                disabled={isLoading}
                className="flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? "Sending..." : "Send Email"}
              </button>
            </div>
          </div>
        </div>

        {/* Right Panel - Preview/Info */}
        <div className="w-full sm:w-1/2 p-4 sm:p-12 bg-white/10 backdrop-blur-md">
          <div className="h-full flex flex-col">
            <h3 className="text-white text-lg font-semibold mb-4">
              Email Preview
            </h3>
            
            <div className="flex-1 bg-white/5 rounded-lg p-4 border border-white/10">
              <div className="space-y-3 text-white/80 text-sm">
                <div>
                  <span className="font-medium">To:</span> {emailData.to || "<EMAIL>"}
                </div>
                <div>
                  <span className="font-medium">Subject:</span> {emailData.subject || "Email subject"}
                </div>
                <div className="border-t border-white/10 pt-3">
                  <span className="font-medium">Message:</span>
                  <div className="mt-2 whitespace-pre-wrap">
                    {emailData.message || "Your message will appear here..."}
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-4 text-white/60 text-xs">
              <p>
                This email will include the current conversation context from chat ID: {activeChat}
              </p>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default EmailSection;

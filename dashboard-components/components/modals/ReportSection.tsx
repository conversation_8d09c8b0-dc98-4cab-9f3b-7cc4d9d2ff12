import { useState, useRef, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { EditOutlined } from "@ant-design/icons";
import useUser from "../../hooks/useUser";
import { ReportSectionProps } from "../../types/dashboard.types";

// You'll need to add this icon to your assets
// import arrowIcon from "../../assets/images/arrowIcon.svg";

interface ReportSection {
  section_name: string;
  section_content: string;
}

const ReportSection: React.FC<ReportSectionProps> = ({ isOpen, onClose, data }) => {
  const { chatId } = useParams<{ chatId: string }>();
  const navigate = useNavigate();
  const { user } = useUser();
  const [activeIndex, setActiveIndex] = useState("introduction");
  const [activeEdit, setActiveEdit] = useState("");
  const [sections, setSections] = useState<ReportSection[]>([
    {
      section_name: "introduction",
      section_content: "<p>This report provides a comprehensive analysis of the conversation and research findings.</p>"
    },
    {
      section_name: "technology",
      section_content: "<p>Technology section content will be populated based on the conversation context.</p>"
    },
    {
      section_name: "healthcare",
      section_content: "<p>Healthcare-related findings and insights from the conversation.</p>"
    },
    {
      section_name: "finance",
      section_content: "<p>Financial aspects and implications discussed in the conversation.</p>"
    },
    {
      section_name: "appendix",
      section_content: "<p>Additional resources and references from the conversation.</p>"
    }
  ]);

  // Simple arrow icon placeholder
  const ArrowIcon = () => (
    <svg width="20" height="17" viewBox="0 0 20 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 1L19 8.5L12 16M19 8.5H1" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  const sectionRefs: Record<string, React.RefObject<HTMLElement | null>> = {
    introduction: useRef(null),
    technology: useRef(null),
    healthcare: useRef(null),
    finance: useRef(null),
    appendix: useRef(null),
  };

  const scrollToSection = (id: string) => {
    setActiveIndex(id);
    sectionRefs[id].current?.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
  };

  const sendEmail = () => {
    const to = user?.email;
    const subject = encodeURIComponent("Report from Mergen AI");
    const body = encodeURIComponent("Please find the attached report from your conversation.");

    const mailtoLink = `mailto:${to}?subject=${subject}&body=${body}`;
    window.location.href = mailtoLink;
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    // Implement download functionality
    console.log("Download report for chat:", chatId);
  };

  // Simple text editor component
  const TextEditor: React.FC<{
    description: string;
    id: number;
    setSections: React.Dispatch<React.SetStateAction<ReportSection[]>>;
    setActiveEdit: React.Dispatch<React.SetStateAction<string>>;
  }> = ({ description, id, setSections, setActiveEdit }) => {
    const [content, setContent] = useState(description);

    const handleSave = () => {
      setSections(prev => prev.map((section, index) => 
        index === id ? { ...section, section_content: content } : section
      ));
      setActiveEdit("");
    };

    return (
      <div className="space-y-4">
        <textarea
          value={content.replace(/<[^>]*>/g, '')} // Strip HTML for editing
          onChange={(e) => setContent(`<p>${e.target.value}</p>`)}
          className="w-full h-32 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <div className="flex gap-2">
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Save
          </button>
          <button
            onClick={() => setActiveEdit("")}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
          >
            Cancel
          </button>
        </div>
      </div>
    );
  };

  useEffect(() => {
    // Load report data based on chatId
    if (chatId && data) {
      // Update sections with actual data
      console.log("Loading report for chat:", chatId);
    }
  }, [chatId, data]);

  return (
    <motion.div
      key="report"
      className="py-20 flex px-6 sm:px-8 lg:px-0 report-section"
    >
      {/* Sidebar Navigation */}
      <aside className="w-[25%] lg:flex flex-col items-center gap-10 hidden">
        <button
          className="flex justify-center bg-[#1E1E1E] p-3 rounded-[50%] cursor-pointer"
          onClick={() => navigate(-1)}
        >
          <div className="rotate-180">
            <ArrowIcon />
          </div>
        </button>
        
        <nav className="flex flex-col">
          {sections.map(({ section_name }) => (
            <button
              key={section_name}
              className={`${
                activeIndex === section_name ? "font-black" : "font-medium"
              } text-[14px] leading-[32px] tracking-[0.01em] text-center cursor-pointer capitalize`}
              onClick={() => scrollToSection(section_name)}
            >
              {section_name}
            </button>
          ))}
        </nav>
      </aside>

      {/* Main Content */}
      <main className="w-full lg:w-[75%] lg:px-16">
        {/* Header */}
        <header className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-8">
          <div>
            <h1 className="font-bold text-[32px] leading-[32px] tracking-[0.01em] font-['Inter']">
              Research Report
            </h1>
            <p className="text-gray-600 mt-2">
              Generated from conversation: {chatId}
            </p>
          </div>
          
          <div className="flex gap-3">
            <button
              onClick={sendEmail}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Email Report
            </button>
            <button
              onClick={handlePrint}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors no-print"
            >
              Print
            </button>
            <button
              onClick={handleDownload}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors no-print"
            >
              Download
            </button>
          </div>
        </header>

        {/* Mobile Navigation */}
        <nav className="lg:hidden mb-8 flex flex-wrap gap-2">
          {sections.map(({ section_name }) => (
            <button
              key={section_name}
              className={`px-3 py-1 rounded text-sm capitalize ${
                activeIndex === section_name
                  ? "bg-blue-600 text-white"
                  : "bg-gray-200 text-gray-700"
              }`}
              onClick={() => scrollToSection(section_name)}
            >
              {section_name}
            </button>
          ))}
        </nav>

        {/* Report Sections */}
        <div className="space-y-12">
          {sections.map(({ section_name, section_content }, index) => (
            <section
              key={section_name}
              ref={sectionRefs[section_name]}
              id={section_name}
              className="w-full font-['Charter']"
            >
              <h2 className="font-bold text-[20px] leading-[32px] tracking-[0.01em] text-start cursor-pointer mb-4 font-['Inter'] capitalize">
                {section_name}{" "}
                <span
                  onClick={() => setActiveEdit(section_name)}
                  className="no-print text-blue-600 hover:text-blue-800 ml-2"
                >
                  <EditOutlined />
                </span>
              </h2>
              
              {activeEdit !== section_name ? (
                <div
                  className="prose max-w-none"
                  dangerouslySetInnerHTML={{ __html: section_content }}
                />
              ) : (
                <TextEditor
                  description={section_content}
                  id={index}
                  setSections={setSections}
                  setActiveEdit={setActiveEdit}
                />
              )}
            </section>
          ))}
        </div>
      </main>
    </motion.div>
  );
};

export default ReportSection;

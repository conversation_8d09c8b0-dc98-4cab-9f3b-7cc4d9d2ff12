import { useState } from "react";
import { Mo<PERSON>, Tooltip } from "antd";
import { motion } from "framer-motion";
import { CardSectionAreaProps, CardData } from "../../types/dashboard.types";

// You'll need to add this icon to your assets
// import MaskGroupIcon from "../../assets/images/MaskGroupIcon.svg";

const CardSectionArea: React.FC<CardSectionAreaProps> = ({ data }) => {
  const [selectedItem, setSelectedItem] = useState<CardData | null>(null);

  // Default data if none provided
  const defaultData: CardData[] = [
    {
      backgroundColor: "#F8DE7E", // Light Yellow
      name: "Technology",
      yearToggle: ["2023", "2024"],
      topics: ["AI", "Machine Learning", "Blockchain", "IoT", "Cloud Computing", "Cybersecurity"],
    },
    {
      backgroundColor: "#E8AE77", // Peach
      name: "Healthcare",
      yearToggle: ["2023", "2024"],
      topics: ["Telemedicine", "Digital Health"],
    },
    {
      backgroundColor: "#D3D3D3", // Light Gray
      name: "Finance",
      yearToggle: ["2023", "2024"],
      topics: ["FinTech", "Cryptocurrency", "Digital Banking"],
    },
    {
      backgroundColor: "#A8E6CF", // Light Green
      name: "Education",
      yearToggle: ["2023", "2024"],
      topics: ["E-Learning", "EdTech", "Online Courses", "Virtual Classrooms"],
    },
    {
      backgroundColor: "#FFB3BA", // Light Pink
      name: "Environment",
      yearToggle: ["2023", "2024"],
      topics: ["Sustainability", "Climate Change", "Renewable Energy"],
    },
    {
      backgroundColor: "#BFBFFF", // Light Purple
      name: "Transportation",
      yearToggle: ["2023", "2024"],
      topics: ["Electric Vehicles", "Autonomous Driving", "Smart Cities"],
    },
  ];

  const cardData = data || defaultData;

  // Simple mask group icon placeholder
  const MaskGroupIcon = () => (
    <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="8" cy="8" r="6" stroke="white" strokeWidth="2" fill="none"/>
        <path d="M8 4V12M4 8H12" stroke="white" strokeWidth="1"/>
      </svg>
    </div>
  );

  const handleCloseModal = () => {
    setSelectedItem(null);
  };

  return (
    <>
      <motion.div
        initial={{ x: 0, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        exit={{ x: 0, opacity: 0 }}
        transition={{ duration: 0.5, ease: "easeInOut" }}
        className="columns-1 gap-0 md:columns-2 lg:columns-3 xl:columns-4"
      >
        {cardData.map((item, index) => (
          <Tooltip
            key={index}
            placement="right"
            title={
              <div className="text-black px-5 py-6 flex flex-col gap-4">
                <h1 className="font-extrabold text-[20px] leading-[100%] tracking-[-0.03em] text-center">
                  Summary
                </h1>
                <p className="font-medium text-[8px] leading-[198%] tracking-[-0.04em]">
                  This card represents the {item.name} category with topics covering various aspects
                  of the field. Click to view more detailed information and explore related content.
                </p>
                <div className="flex justify-center">
                  <span
                    className="w-fit px-5 bg-[#353535] text-white py-2.5 rounded-[22px] font-black text-[6px] leading-[100%] tracking-[-0.04em] cursor-pointer"
                    onClick={() => setSelectedItem(item)}
                  >
                    View More
                  </span>
                </div>
              </div>
            }
          >
            <div
              style={{ backgroundColor: item.backgroundColor }}
              className="break-inside-avoid p-5 cursor-pointer hover:opacity-70 transition-opacity"
              onClick={() => setSelectedItem(item)}
            >
              <div className="flex justify-between px-8">
                <MaskGroupIcon />
                <div className="relative">
                  <span className="absolute top-0.5 -left-6 px-5 bg-[#353535] text-white py-2.5 rounded-[22px] font-black text-[6px] leading-[100%] tracking-[-0.04em]">
                    {item.yearToggle[0]}
                  </span>
                  <span className="px-5 ps-10 bg-white text-[#2C2C2C] py-2.5 rounded-[22px] font-black text-[6px] leading-[100%] tracking-[-0.04em]">
                    {item.yearToggle[1]}
                  </span>
                </div>
              </div>
              
              <div className="px-8 mt-10">
                <h1 className="font-extrabold text-[30px] leading-[100%] tracking-[-0.03em] text-start">
                  {item.name}
                </h1>
                
                <div className="flex flex-wrap gap-2 mt-6">
                  {item.topics.map((topic, topicIndex) => (
                    <span
                      key={topicIndex}
                      className="px-5 bg-[#353535] text-white py-2 rounded-[22px] font-black text-[6px] leading-[100%] tracking-[-0.04em]"
                    >
                      {topic}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </Tooltip>
        ))}
      </motion.div>

      {/* Modal for expanded view */}
      <Modal
        title={selectedItem?.name}
        open={!!selectedItem}
        onCancel={handleCloseModal}
        footer={null}
        width={800}
        styles={{
          body: { maxHeight: "70vh", overflowY: "auto", fontFamily: "Inter" },
        }}
      >
        {selectedItem && (
          <div className="py-5">
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3">Year Range</h3>
              <div className="flex gap-2">
                {selectedItem.yearToggle.map((year, index) => (
                  <span
                    key={index}
                    className="px-4 py-2 bg-gray-100 rounded-lg font-medium"
                  >
                    {year}
                  </span>
                ))}
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-3">Topics</h3>
              <div className="flex flex-wrap gap-2">
                {selectedItem.topics.map((topic, index) => (
                  <span
                    key={index}
                    className="px-4 py-2 bg-[#353535] text-white rounded-full text-sm"
                  >
                    {topic}
                  </span>
                ))}
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">
                This category contains information and research related to {selectedItem.name.toLowerCase()}.
                Explore the topics above to dive deeper into specific areas of interest.
              </p>
            </div>
          </div>
        )}
      </Modal>
    </>
  );
};

export default CardSectionArea;

import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { MenuOutlined } from "@ant-design/icons";
import { AnimatePresence, motion } from "framer-motion";

// You'll need to add these icons to your assets
// import headingTitle from "../../assets/images/headingImg.svg";
// import settingsIcon from "../../assets/images/settingsIcon.svg";

const HeaderSection: React.FC = () => {
  const navigate = useNavigate();
  const [openMobileMenu, setOpenMobileMenu] = useState(false);

  // Simple logo placeholder (replace with your actual logo)
  const LogoPlaceholder = () => (
    <div className="w-[200px] h-[50px] bg-gray-800 text-white flex items-center justify-center rounded cursor-pointer">
      <span className="font-bold text-lg">MERGEN AI</span>
    </div>
  );

  // Simple settings icon placeholder
  const SettingsIcon = () => (
    <svg width="27" height="27" viewBox="0 0 27 27" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M13.5 8.5C10.7 8.5 8.5 10.7 8.5 13.5C8.5 16.3 10.7 18.5 13.5 18.5C16.3 18.5 18.5 16.3 18.5 13.5C18.5 10.7 16.3 8.5 13.5 8.5Z" stroke="currentColor" strokeWidth="2"/>
      <path d="M21.5 13.5C21.5 14.2 21.4 14.9 21.2 15.5L23.5 17.3C23.7 17.5 23.8 17.8 23.6 18.1L21.6 21.4C21.4 21.7 21.1 21.8 20.8 21.7L18.1 20.6C17.4 21.1 16.6 21.5 15.8 21.8L15.4 24.7C15.3 25 15.1 25.2 14.8 25.2H10.8C10.5 25.2 10.3 25 10.2 24.7L9.8 21.8C9 21.5 8.2 21.1 7.5 20.6L4.8 21.7C4.5 21.8 4.2 21.7 4 21.4L2 18.1C1.8 17.8 1.9 17.5 2.1 17.3L4.4 15.5C4.2 14.9 4.1 14.2 4.1 13.5C4.1 12.8 4.2 12.1 4.4 11.5L2.1 9.7C1.9 9.5 1.8 9.2 2 8.9L4 5.6C4.2 5.3 4.5 5.2 4.8 5.3L7.5 6.4C8.2 5.9 9 5.5 9.8 5.2L10.2 2.3C10.3 2 10.5 1.8 10.8 1.8H14.8C15.1 1.8 15.3 2 15.4 2.3L15.8 5.2C16.6 5.5 17.4 5.9 18.1 6.4L20.8 5.3C21.1 5.2 21.4 5.3 21.6 5.6L23.6 8.9C23.8 9.2 23.7 9.5 23.5 9.7L21.2 11.5C21.4 12.1 21.5 12.8 21.5 13.5Z" stroke="currentColor" strokeWidth="2"/>
    </svg>
  );

  const handleLogout = () => {
    // Implement logout logic
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    navigate('/');
  };

  return (
    <>
      <div className="flex justify-between shadow-[0px_3px_8.4px_0px_rgba(0,0,0,0.25)] px-6 sm:px-14 py-8 sm:py-11">
        {/* Left spacer for desktop */}
        <div className="flex-col gap-2.5 flex-1 hidden lg:flex"></div>
        
        {/* Logo - centered on desktop, left on mobile */}
        <div className="flex-1 flex justify-start lg:justify-center">
          <div onClick={() => navigate("/search")}>
            <LogoPlaceholder />
          </div>
        </div>
        
        {/* Right section - desktop */}
        <div className="lg:flex justify-end gap-2.5 items-center flex-1 hidden">
          <div className="cursor-pointer" onClick={handleLogout}>
            <SettingsIcon />
          </div>
        </div>
        
        {/* Mobile menu button */}
        <div className="lg:hidden flex items-center">
          <MenuOutlined
            className="text-2xl cursor-pointer"
            onClick={() => setOpenMobileMenu(true)}
          />
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {openMobileMenu && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 z-[998] lg:hidden"
              onClick={() => setOpenMobileMenu(false)}
            />
            <motion.div
              initial={{ x: "100%" }}
              animate={{ x: 0 }}
              exit={{ x: "100%" }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="fixed top-0 right-0 h-full w-[280px] bg-white z-[999] lg:hidden shadow-lg"
            >
              <div className="p-6">
                <div className="flex justify-between items-center mb-8">
                  <h2 className="text-xl font-bold">Menu</h2>
                  <button
                    onClick={() => setOpenMobileMenu(false)}
                    className="text-2xl"
                  >
                    ×
                  </button>
                </div>
                
                <div className="space-y-4">
                  <button
                    onClick={() => {
                      navigate("/search");
                      setOpenMobileMenu(false);
                    }}
                    className="block w-full text-left py-3 px-4 hover:bg-gray-100 rounded"
                  >
                    Dashboard
                  </button>
                  
                  <button
                    onClick={() => {
                      handleLogout();
                      setOpenMobileMenu(false);
                    }}
                    className="block w-full text-left py-3 px-4 hover:bg-gray-100 rounded text-red-600"
                  >
                    Logout
                  </button>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default HeaderSection;

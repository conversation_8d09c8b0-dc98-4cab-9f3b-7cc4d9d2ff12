import { useState } from "react";
import { Modal } from "antd";

// You'll need to add this info icon to your assets
// import infoIcon from "../../assets/images/InfoIcon.svg";

interface ListCardData {
  summary: string;
  keywords: string[];
  key_points: string[];
}

interface ListCardsProps {
  data: ListCardData;
  setBackUpData?: (data: any) => void;
  referenceData?: any[];
  backupData?: any[];
}

const ListCards: React.FC<ListCardsProps> = ({ data }) => {
  const { summary, keywords: Keywords, key_points: Important_items } = data;
  const [selectedItem, setSelectedItem] = useState<ListCardData | null>(null);
  const [summaryData, setSummarydata] = useState("");
  const [impItems, setImpItems] = useState<string[]>([]);
  const [keywords, setKeywords] = useState<string[]>([]);

  const handleCloseModal = () => {
    setSelectedItem(null);
    setSummarydata("");
    setImpItems([]);
    setKeywords([]);
  };

  // Simple info icon as SVG (replace with your actual icon)
  const InfoIcon = () => (
    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="6" cy="6" r="6" fill="#353535"/>
      <path d="M6 4V8M6 2.5H6.01" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  return (
    <>
      <div className="flex border-t-4 border-[#939393]">
        {/* Desktop Summary Section */}
        <div className="hidden lg:block w-[60%] border-x-[0.5px] border-[#9E9E9E]">
          <div className="text-black px-5 py-6 flex flex-col gap-4 ps-12">
            <h1 className="font-semibold text-[9px] leading-[100%] tracking-[-0.04em]">
              Summary
            </h1>
            <p className="font-medium text-[8px] leading-[198%] tracking-[-0.04em] truncate-text">
              {summary}
            </p>
            <div className="flex justify-end">
              <span
                className="w-fit px-5 bg-[#353535] text-white py-2.5 rounded-[22px] font-black text-[6px] leading-[100%] tracking-[-0.04em] cursor-pointer"
                onClick={() => {
                  setSelectedItem(data);
                  setSummarydata(summary);
                }}
              >
                View More
              </span>
            </div>
          </div>
        </div>

        {/* Keywords and Important Items Section */}
        <div className="w-[100%] lg:w-[46%] border-x-[0.5px] lg:border-s-0 border-[#9E9E9E]">
          <div className="ps-2 lg:ps-0">
            {/* Mobile Summary Section */}
            <div className="flex border-b-[0.5px] border-[#9E9E9E] lg:hidden">
              <div className="w-full relative text-black border-[#9E9E9E] px-5 py-6 flex flex-col gap-3">
                <h1 className="font-semibold text-[9px] leading-[100%] tracking-[-0.04em]">
                  Summary
                </h1>
                <p className="font-medium text-[8px] leading-[198%] tracking-[-0.04em] truncate-text">
                  {summary}
                </p>
                <span
                  className="w-fit px-2.5 bg-[#353535] text-white py-1 rounded-[8.5px] font-medium text-[5px] leading-[100%] tracking-[-0.04em] absolute right-2.5 bottom-2 cursor-pointer"
                  onClick={() => {
                    setSelectedItem(data);
                    setSummarydata(summary);
                  }}
                >
                  View More
                </span>
              </div>
            </div>

            {/* Keywords Section */}
            <div className="relative text-black px-5 py-6 flex flex-col gap-3 border-b-[0.5px] border-[#9E9E9E]">
              <h1 className="font-semibold text-[9px] leading-[100%] tracking-[-0.04em]">
                Keywords
              </h1>
              <div className="flex flex-wrap gap-2 w-full sm:w-[85%]">
                {Keywords?.slice(0, 3).map((item: string, i: number) => (
                  <span
                    key={i}
                    className="px-5 bg-[#353535] text-white py-2 rounded-[22px] font-black text-[6px] leading-[100%] tracking-[-0.04em]"
                  >
                    {item}
                  </span>
                ))}
              </div>
              <span
                className="w-fit px-2.5 bg-[#353535] text-white py-1 rounded-[8.5px] font-medium text-[5px] leading-[100%] tracking-[-0.04em] absolute right-2.5 bottom-2.5 cursor-pointer"
                onClick={() => {
                  setSelectedItem(data);
                  setKeywords(Keywords);
                }}
              >
                View More
              </span>
            </div>

            {/* Important Items Section */}
            <div className="relative text-black px-5 py-6 flex flex-col gap-3">
              <h1 className="font-semibold text-[9px] leading-[100%] tracking-[-0.04em]">
                Important Items
              </h1>
              {Important_items?.slice(0, 2).map((item: string, index: number) => (
                <div className="flex gap-2.5" key={index}>
                  <InfoIcon />
                  <p className="font-medium text-[8px] leading-[198%] tracking-[-0.04em] text-[#222222] truncate-item">
                    {item}
                  </p>
                </div>
              ))}
              <span
                className="w-fit px-2.5 bg-[#353535] text-white py-1 rounded-[8.5px] font-medium text-[5px] leading-[100%] tracking-[-0.04em] absolute right-2.5 bottom-2.5 cursor-pointer"
                onClick={() => {
                  setSelectedItem(data);
                  setImpItems(Important_items);
                }}
              >
                View More
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Modal for expanded content */}
      <Modal
        title={
          summaryData
            ? "Summary"
            : impItems.length
            ? "Important Items"
            : keywords.length
            ? "Keywords"
            : ""
        }
        open={!!selectedItem}
        onCancel={handleCloseModal}
        footer={null}
        width={800}
        styles={{
          body: { maxHeight: "70vh", overflowY: "auto", fontFamily: "Inter" },
        }}
      >
        <div className="py-5">
          {summaryData ? (
            summaryData
          ) : impItems.length ? (
            impItems.map((item: string, index: number) => (
              <div className="flex gap-2.5 mb-2" key={index}>
                <InfoIcon />
                <p className="font-medium text-[8px] leading-[198%] tracking-[-0.04em] text-[#222222]">
                  {item}
                </p>
              </div>
            ))
          ) : keywords.length ? (
            keywords.map((keyword: string, i: number) => (
              <span
                key={i}
                className="px-5 bg-[#353535] text-white py-2 rounded-[22px] font-black text-[6px] leading-[100%] tracking-[-0.04em] ml-4"
              >
                {keyword}
              </span>
            ))
          ) : (
            ""
          )}
        </div>
      </Modal>
    </>
  );
};

export default ListCards;

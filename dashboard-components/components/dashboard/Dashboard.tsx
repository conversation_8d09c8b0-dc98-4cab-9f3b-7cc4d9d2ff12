import { useEffect, useRef, useState } from "react";
import Card<PERSON>ectionArea from "./CardSectionArea";
import FooterSection from "./FooterSection";
import HeaderSection from "./HeaderSection";
import ListViewSection from "./ListViewSection";
import HistoryDrawer from "./HistoryDrawer";
// import drawerOpener from "../../assets/images/drawerOpener.png";
import { Route, Routes } from "react-router-dom";
import { AnimatePresence, motion } from "framer-motion";
import ReportSection from "../modals/ReportSection";
import useWindowResize from "../../hooks/useWindowResize";
import { getConversation } from "../../services/conversationService";
import {
  // createChat,
  getChatByConversationId,
} from "../../services/chatService";
import {
  createMessage,
  getMessageByChatId,
} from "../../services/messageService";
import useLoader from "../../hooks/useLoader";
import {
  createReference,
  getReferencesByMessageId,
} from "../../services/referenceService";

const Dashboard = () => {
  const [isListView] = useState<boolean>(true);

  return (
    <>
      <HeaderSection />
      <Routes>
        <Route
          path="/"
          element={<DashboardSection isListView={isListView} />}
        />
        <Route path="/report/:chatId" element={<ReportSection />} />
      </Routes>
    </>
  );
};

export default Dashboard;

interface DashboardSectionProps {
  isListView: boolean;
}

const DashboardSection = ({ isListView }: { isListView: boolean }) => {
  // const location = useLocation();
  // const { messageReply, references } = location.state;
  const { setLoading } = useLoader();
  const [openEmailSection, setOpenEmailSection] = useState(false);
  const [openHistoryView, setOpenHistoryView] = useState<boolean>(false);
  const { screenWidth } = useWindowResize();
  const [isSticky, setIsSticky] = useState(false);
  const listRef = useRef<HTMLDivElement | null>(null);
  const [inputValue, setInputValue] = useState<string>("");
  const [allConversations, setAllConversations] = useState<[]>([]);
  const [activeConversation, setActiveConversation] = useState<string>("");
  const [activeChat, setActiveChat] = useState<string>("");
  const [messages, setMessages] = useState<any>([]);
  const [currMessage, setCurrMessage] = useState<any>([]);
  // const [isSessionCompleted, setIsSessionCompleted] = useState(false);
  const [referenceData, setReferenceData] = useState([]);
  const [reply,] = useState("");
  const [activePairIndex, setActivePairIndex] = useState(0);
  // console.log("activePairIndexzzzzzzzzzzzzzz", activePairIndex);
  // const [activeUserMsgId, setActiveUserMsgId] = useState<string>("");
  // const [activeAiMsgId, setActiveAiMsgId] = useState<string>("");

  // Placeholder for drawer opener image - replace with actual image path
  const drawerOpener = "/path/to/drawerOpener.png";

  const getAllConversations = async () => {
    try {
      setLoading(true);
      const conversationData = await getConversation();
      setAllConversations(conversationData?.data);
      setActiveConversation(conversationData?.data[0]._id);
      // setLoading(false);
    } catch (error) {
      console.error("Error:", error);
      setLoading(false);
    }
  };

  const fetchDataView = async (userActiveId: string, aiActiveId: string) => {
    if (activeConversation) {
      try {
        setLoading(true);
        const chatData = await getChatByConversationId(activeConversation);
        const messageData = await getMessageByChatId(chatData.data[0]._id);
        const filteredMessages =
          userActiveId && aiActiveId
            ? messageData.data.filter((item: any) => {
                return item._id === userActiveId || item._id === aiActiveId;
              })
            : [];
        const filteredAiMessage =
          userActiveId && aiActiveId
            ? filteredMessages.filter((item: any) => {
                return item._id === aiActiveId;
              })
            : [];
          
        const payload = userActiveId && aiActiveId
          ? filteredAiMessage[0]._id
          : messageData.data[1]?._id;
          
        if (payload) {
          const referenceData = await getReferencesByMessageId(payload);
          setReferenceData(
            referenceData.map((item: any) => item.content).flat(1)
          );
        }
        
        setActiveChat(chatData.data[0]._id);
        setCurrMessage(userActiveId && aiActiveId ? filteredMessages : messageData.data);
        setMessages(messageData.data);
      } catch (error) {
        console.error("Error:", error);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSearch = async () => {
    try {
      setLoading(true);
      // let chatId;
      // if (isSessionCompleted) {
      //   const chatData = await createChat({
      //     title: "Chat",
      //     conversationId: activeConversation,
      //   });
      //   chatId = chatData?.data?._id;
      //   setActiveChat(chatId);
      //   setIsSessionCompleted(false);
      // } else {
      // chatId = activeChat;
      // }

      const messageData = await createMessage({
        chatId: activeChat,
        content: inputValue,
        sender: "user",
        attachedFiles: {},
      });
      if (messageData?.data?.aiMessageCreated?._id && messageData?.data?.references) {
        await createReference({
          messageId: messageData.data.aiMessageCreated._id,
          content: messageData.data.references as any,
          // type: "document",
          // sourceUrl: "https://example.com/article/12345",
        });
      }
      if (messageData?.data?.messageCreated?._id && messageData?.data?.aiMessageCreated?._id) {
        await fetchDataView(
          messageData.data.messageCreated._id,
          messageData.data.aiMessageCreated._id
        );
      }
      const maxPairs = Math.floor(messages.length / 2);
      // console.log("maxPairswwwwwwww", maxPairs);
      setActivePairIndex(maxPairs);

      // setMessages([messageData?.data?.messageCreated]);
      // setReply(messageData?.data?.aiMessageCreated.content);
      // setActiveUserMsgId(messageData?.data?.messageCreated?._id);
      // setActiveAiMsgId(messageData?.data?.aiMessageCreated?._id);
      // setReferenceData(messageData?.data?.references);
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
      setInputValue("");
    }
  };

  const handleNext = () => {
    const maxPairs = Math.floor(messages.length / 2);
    if (activePairIndex < maxPairs - 1) {
      setActivePairIndex((prev: number) => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (activePairIndex > 0) {
      setActivePairIndex((prev: number) => prev - 1);
    }

    //   const userMessage = messages[activePairIndex * 2];
    // const aiMessage = messages[activePairIndex * 2 + 1];

    // const activeUserMessageId = userMessage?._id;
    // const activeAiMessageId = aiMessage?._id;
  };

  const fixFooterPosition = () => {
    if (isListView) {
      if (isSticky) {
        return "sticky mx-auto bottom-0 w-[90%] md:w-[80%] xl:w-[60%] mt-12";
      } else {
        if (openHistoryView) {
          return "absolute left-1/2 transform -translate-x-1/2 bottom-0 w-[90%] md:w-[80%] xl:w-[60%] mt-12";
        } else {
          return "fixed left-1/2 transform -translate-x-1/2 bottom-0 w-[90%] md:w-[80%] xl:w-[60%] mt-12";
        }
      }
    } else {
      return "absolute left-1/2 transform -translate-x-1/2 bottom-0 w-[90%] md:w-[80%] xl:w-[60%] mt-12";
    }
  };

  const fetchDataById = (msg: any) => {
    const userMessage = msg[activePairIndex * 2];
    const aiMessage = msg[activePairIndex * 2 + 1];

    const activeUserMessageId = userMessage?._id;
    const activeAiMessageId = aiMessage?._id;
    // console.log("activeAiMessageId", messages);
    // setActiveUserMsgId(activeUserMessageId);
    // setActiveAiMsgId(activeAiMessageId);
    fetchDataView(activeUserMessageId, activeAiMessageId);
  };

  useEffect(() => {
    if (activeConversation) {
      fetchDataView("", "");
      setActivePairIndex(0);
    }
  }, [activeConversation]);

  useEffect(() => {
    if (activeConversation && messages.length > 0) {
      fetchDataById(messages);
    }
  }, [activePairIndex]);

  useEffect(() => {
    getAllConversations();
  }, []);

  useEffect(() => {
    const checkOverflow = () => {
      if (listRef.current) {
        setIsSticky(
          listRef.current.scrollHeight > listRef.current.clientHeight
        );
      }
    };

    checkOverflow();
    window.addEventListener("resize", checkOverflow);

    return () => {
      window.removeEventListener("resize", checkOverflow);
    };
  }, []);

  useEffect(() => {
    if (listRef.current) {
      setIsSticky(listRef.current.scrollHeight > listRef.current.clientHeight);
    }
  }, [isListView, openHistoryView]);

  // useEffect(() => {
  //   if (activeAiMsgId && activeUserMsgId) {

  //   }
  // }, [activeAiMsgId, activeUserMsgId]);

  // useEffect(() => {
  //   // const filteredData = references.filter((item) => {
  //   //   //   return item.Category === category;
  //   //   // });
  //   //   setReferenceData(references);
  //   // }, [references]);

  //   // useEffect(() => {
  //   //   const SESSION_TIMEOUT = 60 * 60 * 1000;
  //   //   const interval = setInterval(() => {
  //   //     setIsSessionCompleted(true);
  //   //   }, SESSION_TIMEOUT);
  //   //   return () => clearTimeout(interval);
  //   // }, []);

  return (
    <>
      <motion.div className="flex">
        <AnimatePresence>
          {openHistoryView && (
            <motion.div
              initial={{ x: "-100%", width: 0, opacity: 0 }}
              animate={{ x: 0, width: "279px", opacity: 1 }}
              exit={{ x: "-100%", width: 0, opacity: 0 }}
              transition={{ duration: 0.6, ease: "easeInOut" }}
              className={`absolute lg:static ${
                openEmailSection ? "z-0" : "z-50"
              } w-[279px] bg-drawer`}
            >
              <HistoryDrawer
                setOpenHistoryView={setOpenHistoryView}
                allConversations={allConversations}
                activeConversation={activeConversation}
                setActiveConversation={setActiveConversation}
                getAllConversations={getAllConversations}
                setOpenEmailSection={setOpenEmailSection}
                activeChat={activeChat}
              />
            </motion.div>
          )}
        </AnimatePresence>
        <motion.div
          animate={{
            width: openHistoryView
              ? screenWidth > 1023
                ? "calc(100% - 279px)"
                : "100%"
              : "100%",
          }}
          transition={{
            duration: openHistoryView ? 0.6 : 0,
            ease: "easeInOut",
          }}
          ref={listRef}
          className="relative overflow-auto scrollbar-custom listCardContainer"
        >
          {!openHistoryView && (
            <motion.img
              src={drawerOpener}
              whileHover={{ scale: 1.1 }}
              initial={{ opacity: 0, y: 0 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 0 }}
              transition={{ duration: 0.5, ease: "easeInOut" }}
              alt="opener"
              onClick={() => setOpenHistoryView(true)}
              className="fixed lg:top-[25%] top-[30%] left-0 cursor-pointer z-50 h-[40%] md:h-[50%] lg:h-auto"
            />
          )}
          {isListView ? (
            <ListViewSection referenceData={referenceData} />
          ) : (
            <div className="px-6 sm:px-16 mt-6 scrollbar-custom overflow-auto cardContainer">
              <CardSectionArea />
            </div>
          )}
          <div className={`${fixFooterPosition()} footer-Section`}>
            <FooterSection
              messages={currMessage}
              reply={reply}
              handleSearch={handleSearch}
              setInputValue={setInputValue}
              inputValue={inputValue}
              openEmailSection={openEmailSection}
              setOpenEmailSection={setOpenEmailSection}
              activeChat={activeChat}
              handleNext={handleNext}
              handlePrevious={handlePrevious}
              activePairIndex={activePairIndex}
              maxPair={Math.floor(messages.length / 2)}
            />
          </div>
        </motion.div>
      </motion.div>
    </>
  );
};

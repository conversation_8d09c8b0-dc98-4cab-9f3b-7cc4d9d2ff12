import { useState } from "react";
import ListCards from "./ListCards";
import { ListViewSectionProps, ReferenceContent } from "../../types/dashboard.types";

const ListViewSection: React.FC<ListViewSectionProps> = ({ referenceData }) => {
  const [backupData, setBackUpData] = useState<ReferenceContent[]>([]);

  return (
    <>
      {referenceData.length > 0 ? (
        referenceData.map((item: ReferenceContent, index: number) => (
          <ListCards
            key={index}
            data={item}
            setBackUpData={setBackUpData}
            referenceData={referenceData}
            backupData={backupData}
          />
        ))
      ) : (
        <div className="flex justify-center mt-6 text-gray-500">
          No References Found
        </div>
      )}
    </>
  );
};

export default ListViewSection;

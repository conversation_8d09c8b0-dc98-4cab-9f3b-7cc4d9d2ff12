import { useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import SearchBar from "../common/SearchBar";
import EmailSection from "../modals/EmailSection";
import useUser from "../../hooks/useUser";
import { FooterSectionProps } from "../../types/dashboard.types";

// You'll need to add these icons to your assets
// import flowerIcon from "../../assets/images/flowerIcon.svg";
// import magnifyIcon from "../../assets/images/magnifyIcon.svg";
// import arrowIcon from "../../assets/images/arrowIcon.svg";

const FooterSection: React.FC<FooterSectionProps> = ({
  messages,
  reply,
  handleSearch,
  setInputValue,
  inputValue,
  openEmailSection,
  setOpenEmailSection,
  activeChat,
  handleNext,
  handlePrevious,
  activePairIndex,
  maxPair,
}) => {
  const navigate = useNavigate();
  const { user } = useUser();

  // Simple icon placeholders (replace with your actual icons)
  const FlowerIcon = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="10" cy="10" r="8" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M10 6V14M6 10H14" stroke="currentColor" strokeWidth="2"/>
    </svg>
  );

  const MagnifyIcon = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="9" cy="9" r="7" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M21 21L16.65 16.65" stroke="currentColor" strokeWidth="2"/>
    </svg>
  );

  const ArrowIcon = () => (
    <svg width="20" height="17" viewBox="0 0 20 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 1L19 8.5L12 16M19 8.5H1" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  return (
    <>
      <div className="glass-effect rounded-[25px] p-6 sm:p-8">
        {/* Chat Messages Display */}
        {messages.length > 0 && (
          <div className="mb-6 space-y-4 max-h-60 overflow-y-auto scrollbar-custom">
            {messages.map((message, index) => (
              <div
                key={message._id}
                className={`flex ${
                  message.sender === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                <div
                  className={`max-w-[80%] p-3 rounded-lg ${
                    message.sender === 'user'
                      ? 'bg-[#353535] text-white'
                      : 'bg-white text-black border'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <span className="text-xs opacity-70">
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Search Input */}
        <div className="mb-4">
          <SearchBar
            color="#1E1E1E"
            border={false}
            inputValue={inputValue}
            handleSearch={handleSearch}
            setInputValue={setInputValue}
            placeholder="Ask a question..."
            resize={true}
            disable={false}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-center">
          {/* Left side buttons */}
          <div className="flex gap-3">
            <button
              className="bg-white shadow-[0px_2px_6.7px_0px_rgba(0,0,0,0.25)] rounded-[10px] px-4 py-2 flex items-center gap-2 hover:bg-gray-50 transition-colors"
              onClick={() => setOpenEmailSection(true)}
            >
              <FlowerIcon />
              <span className="text-sm font-medium">Email</span>
            </button>
            
            <button
              className="bg-white shadow-[0px_2px_6.7px_0px_rgba(0,0,0,0.25)] rounded-[10px] px-4 py-2 flex items-center gap-2 hover:bg-gray-50 transition-colors"
              onClick={() => navigate(`/report/${activeChat}`)}
            >
              <MagnifyIcon />
              <span className="text-sm font-medium">Report</span>
            </button>
          </div>

          {/* Navigation buttons */}
          {maxPair > 1 && (
            <div className="flex gap-2">
              <button
                className={`cursor-pointer bg-white shadow-[0px_2px_6.7px_0px_rgba(0,0,0,0.25)] rounded-[10px] px-4 py-2 ${
                  activePairIndex === 0 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                }`}
                onClick={handlePrevious}
                disabled={activePairIndex === 0}
              >
                <div className="rotate-180">
                  <ArrowIcon />
                </div>
              </button>
              
              <span className="flex items-center px-3 text-sm font-medium">
                {activePairIndex + 1} / {maxPair}
              </span>
              
              <button
                className={`cursor-pointer bg-white shadow-[0px_2px_6.7px_0px_rgba(0,0,0,0.25)] rounded-[10px] px-4 py-2 ${
                  activePairIndex >= maxPair - 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                }`}
                onClick={handleNext}
                disabled={activePairIndex >= maxPair - 1}
              >
                <ArrowIcon />
              </button>
            </div>
          )}
        </div>

        {/* Email Modal */}
        <AnimatePresence>
          {openEmailSection && (
            <>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-white/50 backdrop-blur-sm z-[499]"
                onClick={() => setOpenEmailSection(false)}
              />
              <EmailSection
                openEmailSection={openEmailSection}
                setOpenEmailSection={setOpenEmailSection}
                activeChat={activeChat}
              />
            </>
          )}
        </AnimatePresence>
      </div>
    </>
  );
};

export default FooterSection;

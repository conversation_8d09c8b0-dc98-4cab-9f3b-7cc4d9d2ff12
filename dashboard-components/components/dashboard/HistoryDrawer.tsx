import { useState } from "react";
import { motion } from "framer-motion";
import { HistoryDrawerProps, Conversation } from "../../types/dashboard.types";

// You'll need to add these icons to your assets
// import closeIcon from "../../assets/images/closeIcon.svg";
// import addIcon from "../../assets/images/addIcon.svg";

const HistoryDrawer: React.FC<HistoryDrawerProps> = ({
  setOpenHistoryView,
  allConversations,
  activeConversation,
  setActiveConversation,
  getAllConversations,
  setOpenEmailSection,
  activeChat,
}) => {
  const [searchQuery, setSearchQuery] = useState("");

  // Simple icon placeholders (replace with your actual icons)
  const CloseIcon = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M15 5L5 15M5 5L15 15" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  const AddIcon = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10 5V15M5 10H15" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  const filteredConversations = allConversations.filter((conversation: Conversation) =>
    conversation.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleConversationSelect = (conversationId: string) => {
    setActiveConversation(conversationId);
    setOpenHistoryView(false);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <div className="h-full bg-gradient-to-b from-gray-900 to-gray-800 text-white flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-gray-700">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Conversations</h2>
          <button
            onClick={() => setOpenHistoryView(false)}
            className="p-2 hover:bg-gray-700 rounded-full transition-colors"
          >
            <CloseIcon />
          </button>
        </div>
        
        {/* Search Input */}
        <div className="relative">
          <input
            type="text"
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-gray-700 text-white placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto scrollbar-custom p-4">
        <div className="space-y-2">
          {filteredConversations.length > 0 ? (
            filteredConversations.map((conversation: Conversation) => (
              <motion.div
                key={conversation._id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`p-4 rounded-lg cursor-pointer transition-colors ${
                  activeConversation === conversation._id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 hover:bg-gray-600'
                }`}
                onClick={() => handleConversationSelect(conversation._id)}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium truncate mb-1">
                      {conversation.title}
                    </h3>
                    <p className="text-sm opacity-70">
                      {formatDate(conversation.updatedAt.toString())}
                    </p>
                    {conversation.isDeepResearch && (
                      <span className="inline-block mt-2 px-2 py-1 text-xs bg-green-600 rounded-full">
                        Deep Research
                      </span>
                    )}
                  </div>
                </div>
              </motion.div>
            ))
          ) : (
            <div className="text-center text-gray-400 py-8">
              {searchQuery ? 'No conversations found' : 'No conversations yet'}
            </div>
          )}
        </div>
      </div>

      {/* Footer Actions */}
      <div className="p-4 border-t border-gray-700">
        <button
          onClick={() => {
            // Implement new conversation logic
            console.log('Create new conversation');
          }}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg flex items-center justify-center gap-2 transition-colors"
        >
          <AddIcon />
          <span>New Conversation</span>
        </button>
        
        <button
          onClick={() => {
            setOpenEmailSection(true);
            setOpenHistoryView(false);
          }}
          className="w-full mt-2 bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors"
        >
          Email Current Chat
        </button>
      </div>
    </div>
  );
};

export default HistoryDrawer;

import { useRef, useEffect, useCallback } from "react";
import { useLocation } from "react-router-dom";

// You'll need to add this arrow icon to your assets
// import arrowIcon from "../../assets/images/arrowIcon.svg";

interface SearchBarProps {
  color: string;
  border: boolean;
  inputValue: string;
  handleSearch: () => void;
  setInputValue: (value: string) => void;
  placeholder: string;
  resize: boolean;
  disable: boolean;
  onPaste?: React.ClipboardEventHandler<HTMLTextAreaElement | HTMLInputElement>;
}

const SearchBar: React.FC<SearchBarProps> = ({
  color,
  border,
  handleSearch,
  setInputValue,
  inputValue,
  placeholder,
  disable,
  resize,
  onPaste,
}) => {
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  const location = useLocation();
  const MAX_HEIGHT = 144; // 24px (line-height) * 6 rows

  const adjustHeight = useCallback(() => {
    if (!resize) return;
    const textarea = textAreaRef.current;
    if (textarea) {
      textarea.style.height = "auto";
      const scrollHeight = textarea.scrollHeight;
      textarea.style.height = Math.min(scrollHeight, MAX_HEIGHT) + "px";
      textarea.style.overflowY = scrollHeight > MAX_HEIGHT ? "auto" : "hidden";
    }
  }, [resize]);

  useEffect(() => {
    adjustHeight();
  }, [inputValue, adjustHeight, resize]);

  // Simple arrow icon as SVG (replace with your actual icon)
  const ArrowIcon = () => (
    <svg width="20" height="17" viewBox="0 0 20 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 1L19 8.5L12 16M19 8.5H1" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  return (
    <div className="relative">
      {resize ? (
        <textarea
          ref={textAreaRef}
          rows={1}
          style={{
            resize: "none",
            lineHeight: "24px",
            height: resize ? "auto" : "56px", // 24px line-height + 16px padding-y x2
            overflowY: resize ? "auto" : "hidden",
          }}
          className={`w-[95%] bg-[#F6F6F6] py-4 rounded-[25px] ${
            location.pathname === "/search" && resize
              ? "sm:pe-7 pe-14 ps-2"
              : "ps-7 pe-14"
          } outline-none textarea-hide-scrollbar ${
            border ? "border border-black" : ""
          }`}
          onChange={(e) => {
            setInputValue(e.target.value);
            adjustHeight();
          }}
          onKeyDown={(e) => {
            if (e.key === "Enter" && !disable) {
              handleSearch();
            }
          }}
          onPaste={onPaste}
          placeholder={placeholder}
          value={inputValue}
        />
      ) : (
        <input
          type="text"
          className={`w-[95%] bg-[#F6F6F6] py-4 rounded-[25px] ${
            location.pathname === "/search" && resize
              ? "pe-7 ps-2"
              : "ps-7 pe-16"
          } outline-none textarea-hide-scrollbar ${
            border ? "border border-black" : ""
          }`}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter" && !disable) {
              handleSearch();
            }
          }}
          onPaste={onPaste}
          placeholder={placeholder}
          value={inputValue}
        />
      )}

      <button
        className={`bg-[${
          color ? color : "#1E1E1E"
        }] py-4.5 px-6 rounded-[25px] absolute ${
          border ? "-top-[1px] " : "top-0"
        } -right-2 ${!disable ? "cursor-pointer" : "cursor-not-allowed"}`}
        onClick={handleSearch}
        disabled={disable}
      >
        <ArrowIcon />
      </button>
    </div>
  );
};

export default SearchBar;

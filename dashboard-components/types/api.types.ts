// API Response Types

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Message API Types

export interface CreateMessageRequest {
  chatId: string;
  content: string;
  sender: 'user' | 'ai';
  isDeepResearch?: boolean;
  attachedFiles?: {
    image?: File[];
    audio?: File[];
    url?: string[];
    file?: File[];
  };
}

export interface CreateMessageResponse {
  messageCreated: {
    _id: string;
    chatId: string;
    content: string;
    sender: 'user' | 'ai';
    timestamp: Date;
    conversationId: string;
    metadata: Record<string, any>;
  };
  aiMessageCreated?: {
    _id: string;
    chatId: string;
    content: string;
    sender: 'ai';
    timestamp: Date;
    conversationId: string;
    metadata: Record<string, any>;
  };
  references?: any[];
}

export interface GetMessagesResponse {
  _id: string;
  chatId: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  conversationId: string;
  metadata: Record<string, any>;
}

// Conversation API Types

export interface CreateConversationRequest {
  title: string;
  isDeepResearch?: boolean;
}

export interface ConversationResponse {
  _id: string;
  title: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  isDeepResearch: boolean;
}

export interface UpdateConversationRequest {
  title?: string;
  isDeepResearch?: boolean;
}

// Chat API Types

export interface CreateChatRequest {
  conversationId: string;
}

export interface ChatResponse {
  _id: string;
  conversationId: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

// Reference API Types

export interface CreateReferenceRequest {
  messageId: string;
  content: ReferenceContentData;
}

export interface ReferenceContentData {
  summary: string;
  keywords: string[];
  key_points: string[];
  category?: string;
  effective_start_date?: string;
  effective_end_date?: string;
  citations?: any[];
}

export interface ReferenceResponse {
  _id: string;
  messageId: string;
  content: ReferenceContentData;
  createdAt: Date;
  updatedAt: Date;
}

// User API Types

export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignUpRequest {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface AuthResponse {
  user: {
    _id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  token: string;
  refreshToken?: string;
}

export interface UserProfileResponse {
  _id: string;
  name: string;
  email: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

// AI Server Types

export interface AIProcessRequest {
  user: {
    id: string;
    name: string;
    email: string;
  };
  question: string;
  conversation: any[];
  session_id: string;
  new_files: boolean;
}

export interface AIProcessResponse {
  execution_result: {
    answer: string;
    citations: any[];
    metadata?: Record<string, any>;
  };
  session_id: string;
  status: 'success' | 'error';
  error?: string;
}

// File Upload Types

export interface FileUploadResponse {
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
}

// Error Types

export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: Record<string, any>;
}

// Search and Filter Types

export interface SearchParams {
  query?: string;
  category?: string;
  dateFrom?: string;
  dateTo?: string;
  limit?: number;
  page?: number;
}

export interface FilterOptions {
  categories: string[];
  dateRange: {
    start: Date;
    end: Date;
  };
  keywords: string[];
}

// Email API Types

export interface SendEmailRequest {
  to: string;
  subject: string;
  content: string;
  attachments?: File[];
  chatId?: string;
}

export interface EmailResponse {
  messageId: string;
  status: 'sent' | 'failed';
  error?: string;
}

// Report API Types

export interface GenerateReportRequest {
  chatId: string;
  format: 'pdf' | 'docx' | 'html';
  includeReferences: boolean;
  includeMessages: boolean;
}

export interface ReportResponse {
  reportId: string;
  downloadUrl: string;
  format: string;
  generatedAt: Date;
  expiresAt: Date;
}

// Webhook Types

export interface WebhookPayload {
  event: string;
  data: Record<string, any>;
  timestamp: Date;
  signature?: string;
}

// Analytics Types

export interface AnalyticsData {
  totalConversations: number;
  totalMessages: number;
  averageResponseTime: number;
  topKeywords: string[];
  usageByDate: {
    date: string;
    count: number;
  }[];
}

// Settings Types

export interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  notifications: {
    email: boolean;
    push: boolean;
    inApp: boolean;
  };
  privacy: {
    shareData: boolean;
    analytics: boolean;
  };
}

// Dashboard Component Types

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

export interface Message {
  _id: string;
  chatId: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface Conversation {
  _id: string;
  title: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  isDeepResearch?: boolean;
}

export interface Chat {
  _id: string;
  conversationId: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Reference {
  _id: string;
  messageId: string;
  content: ReferenceContent;
  createdAt: Date;
}

export interface ReferenceContent {
  summary: string;
  keywords: string[];
  key_points: string[];
  category?: string;
  effective_start_date?: string;
  effective_end_date?: string;
}

// Component Props Types

export interface SearchBarProps {
  color: string;
  border: boolean;
  inputValue: string;
  handleSearch: () => void;
  setInputValue: (value: string) => void;
  placeholder: string;
  resize: boolean;
  disable: boolean;
  onPaste?: React.ClipboardEventHandler<HTMLTextAreaElement | HTMLInputElement>;
}

export interface ListCardData {
  summary: string;
  keywords: string[];
  key_points: string[];
}

export interface ListCardsProps {
  data: ListCardData;
  setBackUpData?: (data: any) => void;
  referenceData?: any[];
  backupData?: any[];
}

export interface DashboardProps {
  isListView: boolean;
}

export interface FooterSectionProps {
  messages: Message[];
  reply: string;
  handleSearch: () => void;
  setInputValue: (value: string) => void;
  inputValue: string;
  openEmailSection: boolean;
  setOpenEmailSection: (open: boolean) => void;
  activeChat: string;
  handleNext: () => void;
  handlePrevious: () => void;
  activePairIndex: number;
  maxPair: number;
}

export interface HistoryDrawerProps {
  setOpenHistoryView: (open: boolean) => void;
  allConversations: Conversation[];
  activeConversation: string;
  setActiveConversation: (id: string) => void;
  getAllConversations: () => void;
  setOpenEmailSection: (open: boolean) => void;
  activeChat: string;
}

export interface ListViewSectionProps {
  referenceData: ReferenceContent[];
}

// State Management Types

export interface DashboardState {
  // Input & UI State
  inputValue: string;
  openEmailSection: boolean;
  openHistoryView: boolean;
  isSticky: boolean;
  
  // Conversation Management
  allConversations: Conversation[];
  activeConversation: string;
  activeChat: string;
  
  // Message Management
  messages: Message[];
  currMessage: Message[];
  reply: string;
  activePairIndex: number;
  
  // Results Data
  referenceData: ReferenceContent[];
}

// Context Types

export interface UserContextType {
  user: User | null;
  setUser: (user: User | null) => void;
  loadUserFromStorage: () => void;
}

export interface LoaderContextType {
  loading: boolean;
  setLoading: (loading: boolean) => void;
}

// Hook Types

export interface WindowSize {
  screenWidth: number;
  screenHeight: number;
}

// File Upload Types

export interface AttachedFiles {
  image?: File[];
  audio?: File[];
  url?: string[];
  file?: File[];
}

export interface CreateMessagePayload {
  chatId: string;
  content: string;
  sender: 'user' | 'ai';
  attachedFiles: AttachedFiles;
}

// Animation Types

export interface MotionVariants {
  hidden: {
    x?: string | number;
    y?: string | number;
    opacity?: number;
    width?: string | number;
  };
  visible: {
    x?: string | number;
    y?: string | number;
    opacity?: number;
    width?: string | number;
  };
  exit: {
    x?: string | number;
    y?: string | number;
    opacity?: number;
    width?: string | number;
  };
}

// Email Section Types

export interface EmailSectionProps {
  openEmailSection: boolean;
  setOpenEmailSection: (open: boolean) => void;
  activeChat: string;
}

// Report Section Types

export interface ReportSectionProps {
  isOpen: boolean;
  onClose: () => void;
  data?: any;
}

// Card Section Types

export interface CardData {
  backgroundColor: string;
  name: string;
  yearToggle: string[];
  topics: string[];
}

export interface CardSectionAreaProps {
  data?: CardData[];
}

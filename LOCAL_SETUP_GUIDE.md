# Mergen AI Web Application - Local Setup Guide

## 📋 Repository Overview

The repository contains a full-stack application with:
- **Frontend**: React + TypeScript + Vite application with Ant Design UI components
- **Backend**: Node.js + Express API server with MongoDB database
- **Dashboard Components**: Extracted reusable dashboard components collection

## 🏗️ Architecture

- **Frontend Port**: 5173 (Vite dev server)
- **Backend Port**: 5000 (Express server)
- **Database**: MongoDB (requires connection string)
- **API Base URL**: Currently configured to use `https://backend.mergenai.io/api/v1`

## 🚀 Local Setup Instructions

### Prerequisites

Make sure you have installed:
- **Node.js** (v16 or higher)
- **npm** or **yarn**
- **MongoDB** (local instance or MongoDB Atlas)

### 1. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Create environment file
touch .env
```

**Required Environment Variables** (create `.env` file in backend directory):
```env
# Database
MONGODB_URI=mongodb://localhost:27017/mergen-ai
# or use MongoDB Atlas: mongodb+srv://username:<EMAIL>/mergen-ai

# Server
PORT=5000

# JWT Secret
JWT_SECRET=your-jwt-secret-key

# Email Configuration (for password reset)
EMAIL=<EMAIL>
EMAIL_PASS=your-email-password

# Stripe Configuration (for payments)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key

# Product IDs for subscription plans
FREE_PRODUCT_ID=prod_free_plan_id
PRO_PRODUCT_ID=prod_pro_plan_id
BUSINESS_PRODUCT_ID=prod_business_plan_id

# AWS S3 (for file uploads)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-s3-bucket
```

**Start the backend server:**
```bash
npm start
# This runs: npx nodemon server.js
# Server will start at http://localhost:5000
```

### 2. Frontend Setup

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev
# This runs: vite
# Frontend will start at http://localhost:5173
```

### 3. Configure Frontend API Connection

For local development, you need to update the API base URL:

**Edit `frontend/src/services/axiosInstance.ts`:**
```typescript
// Change this line:
const BASE_URL = "https://backend.mergenai.io/api/v1";

// To this for local development:
const BASE_URL = "http://localhost:5000/api/v1";
```

## 🎯 Accessing the Dashboard

### Application Routes

1. **Login Page**: `http://localhost:5173/` (default route)
2. **Sign Up**: `http://localhost:5173/signUp`
3. **Dashboard**: `http://localhost:5173/dashboard/` (requires authentication)
4. **Search Page**: `http://localhost:5173/search` (requires authentication)

### Test Account

Use the provided test credentials:
- **Email**: `<EMAIL>`
- **Password**: `leo123`

### Dashboard Features

The dashboard includes:
- **Header Section**: Navigation and user menu
- **History Drawer**: Conversation sidebar (animated)
- **Main Content Area**: Chat interface and results
- **Footer Section**: Message input with file upload support
- **Report Generation**: Accessible via `/dashboard/report/:chatId`

## 🛠️ Development Commands

### Frontend Commands
```bash
cd frontend

# Development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run linting
npm run lint

# Run tests
npm test
```

### Backend Commands
```bash
cd backend

# Start with nodemon (auto-restart)
npm start

# Start without nodemon
node server.js
```

## 🔧 Key Technologies Used

### Frontend Stack
- **React 18.3.1** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS 4.0.14** for styling
- **Ant Design 5.24.4** for UI components
- **Framer Motion 12.5.0** for animations
- **React Router DOM 7.3.0** for routing
- **Axios 1.8.4** for API calls

### Backend Stack
- **Express 4.21.2** web framework
- **Mongoose 8.13.0** for MongoDB
- **Socket.io 4.8.1** for real-time communication
- **JWT** for authentication
- **Stripe** for payment processing
- **AWS SDK** for S3 file storage

## 📱 Dashboard Components

The repository includes a comprehensive dashboard components collection in the `dashboard-components/` folder with:

- **Reusable Components**: SearchBar, ListCards, HistoryDrawer
- **Context Providers**: User management, Loading states
- **Custom Hooks**: Window resize, User context, Loader
- **Service Layer**: API integration for messages, conversations, references
- **TypeScript Interfaces**: Complete type definitions

## 🔍 Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure backend is running on port 5000 and frontend on 5173
2. **Database Connection**: Verify MongoDB is running and connection string is correct
3. **Authentication Issues**: Check JWT secret is set in backend environment
4. **API Calls Failing**: Verify the BASE_URL in axiosInstance.ts points to your local backend

### Database Setup

If using local MongoDB:
```bash
# Start MongoDB service
mongod

# Or if using Homebrew on macOS:
brew services start mongodb-community
```

### Environment Variables Check

Verify all required environment variables are set in the backend `.env` file. The application will fail to start without proper database and JWT configuration.

## 🚀 Production Deployment

The application is configured for deployment on:
- **Frontend**: Vercel (configured in `frontend/vercel.json`)
- **Backend**: Vercel or any Node.js hosting (configured in `backend/vercel.json`)

For production, ensure all environment variables are properly set in your hosting platform.

## 📂 Project Structure

```
mergen-web-app/
├── README.md
├── backend/                 # Node.js Express API
│   ├── config/             # Database and service configs
│   ├── controllers/        # Route handlers
│   ├── models/            # MongoDB schemas
│   ├── routes/            # API routes
│   ├── services/          # Business logic
│   ├── package.json
│   └── server.js          # Entry point
├── frontend/              # React TypeScript app
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── services/      # API integration
│   │   ├── context/       # State management
│   │   └── hooks/         # Custom hooks
│   ├── package.json
│   └── vite.config.ts     # Vite configuration
└── dashboard-components/  # Extracted components
    ├── components/        # Reusable dashboard components
    ├── hooks/            # Custom hooks
    ├── services/         # API services
    └── types/            # TypeScript definitions
```

## 🎨 Dashboard Component Usage

### Main Dashboard Component

The dashboard is accessible at `/dashboard/` and includes several key sections:

```typescript
// Main dashboard structure
<Dashboard />
├── <HeaderSection />           // Navigation and user menu
├── <Routes>
│   ├── <DashboardSection />   // Main chat interface
│   └── <ReportSection />      // Report generation
└── <FooterSection />          // Message input area
```

### Key Dashboard Features

1. **Chat Interface**: Real-time messaging with AI
2. **History Drawer**: Animated sidebar for conversation history
3. **File Upload**: Support for images, documents, and URLs
4. **Report Generation**: Create detailed reports from conversations
5. **Responsive Design**: Mobile-first approach with adaptive layouts

### Component Hierarchy

```
DashboardSection
├── HistoryDrawer (animated sidebar)
├── ListViewSection (results container)
│   └── ListCards (individual result cards)
├── CardSectionArea (alternative masonry view)
└── FooterSection (chat input)
    └── SearchBar (message input with file upload)
```

## 🔐 Authentication Flow

1. **Login**: User authenticates at `/` route
2. **Token Storage**: JWT token stored in localStorage
3. **Protected Routes**: Dashboard routes require authentication
4. **Auto-redirect**: Unauthenticated users redirected to login

### API Authentication

```typescript
// JWT token automatically added to requests
ProtectedAPI.interceptors.request.use((config) => {
    const token = localStorage.getItem("authToken");
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});
```

## 🎯 Quick Start Checklist

- [ ] Install Node.js (v16+)
- [ ] Install MongoDB or get MongoDB Atlas connection string
- [ ] Clone repository
- [ ] Set up backend environment variables
- [ ] Install backend dependencies (`cd backend && npm install`)
- [ ] Start backend server (`npm start`)
- [ ] Install frontend dependencies (`cd frontend && npm install`)
- [ ] Update API base URL in axiosInstance.ts
- [ ] Start frontend server (`npm run dev`)
- [ ] Access application at `http://localhost:5173`
- [ ] Login with test account: `<EMAIL>` / `leo123`

## 📝 Additional Notes

- **Socket.io**: Real-time communication enabled on `/sockets` path
- **File Uploads**: Configured for AWS S3 storage
- **Payment Integration**: Stripe integration for subscription management
- **Email Service**: Nodemailer configured for password reset emails
- **API Documentation**: OpenAPI spec available in `backend/openapi_spec.YAML`

This setup will give you a fully functional local development environment with the complete Mergen AI dashboard interface!

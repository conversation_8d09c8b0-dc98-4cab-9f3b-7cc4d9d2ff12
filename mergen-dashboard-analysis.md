# Mergen AI Dashboard - Frontend Analysis & Implementation Blueprint

## Overview
React + TypeScript dashboard with chat interface, results display, and conversation management. Uses Tailwind CSS, Framer Motion, and Ant Design components.

## Tech Stack
- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS 4.0, Custom CSS
- **Animation**: Framer Motion
- **UI Components**: Ant Design
- **Backend**: Node.js, Express, MongoDB
- **API**: Axios with interceptors

## Architecture Overview

### Core Components Structure
```
Dashboard/
├── HeaderSection - Navigation & user menu
├── HistoryDrawer - Conversation sidebar
├── ListViewSection - Results display
├── FooterSection - Chat interface
└── SearchBar - Reusable input component
```

### State Management Pattern
```typescript
// Main dashboard state
const [inputValue, setInputValue] = useState<string>("");
const [activeConversation, setActiveConversation] = useState<string>("");
const [messages, setMessages] = useState<[]>([]);
const [referenceData, setReferenceData] = useState<[]>([]);
```

## Design System

### Color Palette
- **Primary**: `#1E1E1E`, `#353535` (dark UI elements)
- **Background**: `#F6F6F6`, `#FFFFFF` (light surfaces)
- **Accent**: `#939393`, `#9E9E9E` (borders, secondary)
- **Cards**: `#F8DE7E`, `#E8AE77` (dynamic backgrounds)

### Typography
- **Primary**: Inter (UI, lists, cards)
- **Secondary**: Charter (footer, special sections)
- **Scale**: 5px-70px with responsive breakpoints

### Layout System
- **Grid**: CSS columns (1-4 based on screen size)
- **Breakpoints**: sm(640px), md(768px), lg(1024px), xl(1280px)
- **Spacing**: 2-10 scale (8px-40px)

## Chat Interface Implementation

### Input Component (SearchBar)
```typescript
// Auto-resizing textarea with file support
<textarea
  ref={textAreaRef}
  style={{ height: "auto", maxHeight: "144px" }}
  onKeyDown={(e) => e.key === "Enter" && handleSearch()}
  onPaste={handleFileUpload}
/>
```

### Message Flow
1. User input → `createMessage()` API call
2. Backend processes with AI server
3. Response + references stored
4. UI updates with new message pair
5. References displayed in results panel

### API Integration
```typescript
// Message creation with file support
const messageData = await createMessage({
  chatId: activeChat,
  content: inputValue,
  sender: "user",
  attachedFiles: { image: [], audio: [], url: [], file: [] }
});
```

## Results Panel System

### Data Structure
```typescript
interface ResultItem {
  summary: string;
  keywords: string[];
  key_points: string[];
}
```

### Display Components
- **ListCards**: Individual result items with sections
- **Modal System**: Expandable detailed views
- **Truncation**: CSS-based text limiting (1-8 lines)

### Layout Patterns
```css
/* Desktop: 3-column layout */
.desktop-layout {
  display: flex;
  /* Date | Summary | Keywords+Items */
}

/* Mobile: Single column stack */
.mobile-layout {
  width: 100%;
  /* Keywords and Items only */
}
```

## Key Reusable Patterns

### 1. Glass Morphism Effect
```css
.glass-effect {
  background: linear-gradient(168.12deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0.1) 98.85%);
  backdrop-filter: blur(40px);
  box-shadow: 0px 4px 24px -1px rgba(136,136,136,0.12);
}
```

### 2. Animation Patterns
```typescript
// Framer Motion slide-in
<motion.div
  initial={{ x: "-100%", opacity: 0 }}
  animate={{ x: 0, opacity: 1 }}
  exit={{ x: "-100%", opacity: 0 }}
  transition={{ duration: 0.6, ease: "easeInOut" }}
/>
```

### 3. Responsive Text Truncation
```css
.truncate-text {
  display: -webkit-box;
  -webkit-line-clamp: 8;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
```

### 4. Custom Scrollbars
```css
.scrollbar-custom::-webkit-scrollbar {
  width: 6px;
}
.scrollbar-custom::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}
```

## Implementation Guidelines

### Component Structure
1. **Functional Components** with TypeScript interfaces
2. **Custom Hooks** for window resize, user context, loading states
3. **Service Layer** with Axios interceptors for API calls
4. **Context Providers** for global state (User, Loader)

### Styling Approach
1. **Tailwind Classes** for layout and spacing
2. **Custom CSS** for complex animations and effects
3. **Ant Design** for modals, tooltips, complex components
4. **Framer Motion** for page transitions and interactions

### State Management
1. **Local State** with useState for component-specific data
2. **Context API** for global user and loading states
3. **Service Calls** with async/await pattern
4. **Error Handling** with try-catch blocks

### Responsive Strategy
1. **Mobile-first** approach with progressive enhancement
2. **Conditional Rendering** based on screen size
3. **Dynamic Classes** using template literals
4. **Flexible Grid** with CSS columns

## Key Dependencies
```json
{
  "react": "^18.3.1",
  "typescript": "latest",
  "tailwindcss": "^4.0.14",
  "framer-motion": "^12.5.0",
  "antd": "^5.24.4",
  "axios": "^1.8.4",
  "react-router-dom": "^7.3.0"
}
```

## Performance Optimizations
- **Code Splitting**: Manual chunks for React, Ant Design, Quill, Motion
- **Image Optimization**: Background images with proper sizing
- **Lazy Loading**: AnimatePresence for conditional components
- **Memoization**: Strategic use of React hooks

This blueprint provides the foundation for implementing similar chat-based dashboard interfaces with modern React patterns and responsive design principles.
